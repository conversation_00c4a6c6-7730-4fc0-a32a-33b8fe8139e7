-- Migration: Add Picklist Tags Support
-- This migration adds support for picklist tags by:
-- 1. Adding a 'type' column to the existing tags table
-- 2. Creating picklist_values table for storing predefined values
-- 3. Creating task_picklist_selections table for storing selected values

-- Step 1: Add type column to tags table with default value 'regular'
ALTER TABLE tags ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'regular';

-- Step 2: Create picklist_values table
CREATE TABLE IF NOT EXISTS picklist_values (
    id TEXT PRIMARY KEY,
    tag_id TEXT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    value TEXT NOT NULL,
    position INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Step 3: Create task_picklist_selections table
CREATE TABLE IF NOT EXISTS task_picklist_selections (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    tag_id TEXT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    picklist_value_id TEXT NOT NULL REFERENCES picklist_values(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Step 4: Enable RLS on new tables
ALTER TABLE picklist_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_picklist_selections ENABLE ROW LEVEL SECURITY;

-- Step 5: Create RLS policies for picklist_values
CREATE POLICY "Users can view their own picklist values" ON picklist_values
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = picklist_values.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can insert their own picklist values" ON picklist_values
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = picklist_values.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can update their own picklist values" ON picklist_values
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = picklist_values.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can delete their own picklist values" ON picklist_values
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = picklist_values.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

-- Step 6: Create RLS policies for task_picklist_selections
CREATE POLICY "Users can view their own task picklist selections" ON task_picklist_selections
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM tasks
            WHERE tasks.id = task_picklist_selections.task_id 
            AND tasks.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can insert their own task picklist selections" ON task_picklist_selections
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM tasks
            WHERE tasks.id = task_picklist_selections.task_id 
            AND tasks.user_id = auth.user_id()
        ) AND EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = task_picklist_selections.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can update their own task picklist selections" ON task_picklist_selections
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM tasks
            WHERE tasks.id = task_picklist_selections.task_id 
            AND tasks.user_id = auth.user_id()
        ) AND EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = task_picklist_selections.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can delete their own task picklist selections" ON task_picklist_selections
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM tasks
            WHERE tasks.id = task_picklist_selections.task_id 
            AND tasks.user_id = auth.user_id()
        ) AND EXISTS (
            SELECT 1 FROM tags
            WHERE tags.id = task_picklist_selections.tag_id 
            AND tags.user_id = auth.user_id()
        )
    );

-- Step 7: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_picklist_values_tag_id ON picklist_values(tag_id);
CREATE INDEX IF NOT EXISTS idx_picklist_values_position ON picklist_values(tag_id, position);
CREATE INDEX IF NOT EXISTS idx_task_picklist_selections_task_id ON task_picklist_selections(task_id);
CREATE INDEX IF NOT EXISTS idx_task_picklist_selections_tag_id ON task_picklist_selections(tag_id);
CREATE INDEX IF NOT EXISTS idx_task_picklist_selections_task_tag ON task_picklist_selections(task_id, tag_id);

-- Migration complete
-- All existing tags will have type='regular' by default
-- New picklist functionality is now available
