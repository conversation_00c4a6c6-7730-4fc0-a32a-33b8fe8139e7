// Centralized field length limits for user-entered text
// Adjust as needed

export const LIMITS = {
  TASK_TITLE: 200,
  TASK_DESCRIPTION: 10000,

  LIST_NAME: 120,
  LIST_DESCRIPTION: 5000,

  SPACE_NAME: 100,
  SPACE_DESCRIPTION: 5000,

  TAG_NAME: 50,
  PICKLIST_VALUE: 50,

  ICON_NAME: 50,
  COLOR_VALUE: 20,
  EMAIL: 254,
} as const;

// User data usage limits
export const USAGE_LIMITS = {
  SPACES: 10,
  LISTS: 100,
  TASKS: 1000, // Including subtasks
} as const;

export type LimitKey = keyof typeof LIMITS;

export function clampText(value: string | null | undefined, max: number): string | null {
  if (value === null || value === undefined) return null;
  const trimmed = value.trim();
  if (trimmed.length <= max) return trimmed;
  return trimmed.slice(0, max);
}

