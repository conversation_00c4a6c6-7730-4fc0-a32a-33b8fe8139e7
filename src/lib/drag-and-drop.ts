import {
  <PERSON><PERSON><PERSON><PERSON>,
  TouchSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  pointer<PERSON>ithin,
  closestCorners,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";

/**
 * Standardized drag and drop configuration for consistent behavior
 * across all task management contexts
 */

/**
 * Optimized sensors for both desktop and mobile with haptic feedback support
 */
export function useDragSensors() {
  return useSensors(
    // Mouse sensor for desktop - slightly more sensitive for better control
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 5, // Reduced from 8px for more responsive dragging
      },
    }),
    // Touch sensor for mobile with press-and-hold - optimized for tall cards
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250, // Increased back to 250ms to prevent accidental activation
        tolerance: 5, // Reduced tolerance to prevent position jumps
      },
    }),
    // Keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
}

/**
 * Custom collision detection that allows returning to original position
 * and prevents position jumps on mobile
 */
export function customCollisionDetection(args: any) {
  try {
    const { active, droppableContainers, pointerCoordinates } = args;

    // Validate input arguments
    if (!active || !active.id) {
      console.warn('Invalid collision detection args: missing active element');
      return [];
    }

    // For mobile, be more conservative with collision detection to prevent jumps
    const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

    // First try pointer-based detection for more precise control
    let pointerCollisions: any[] = [];
    try {
      pointerCollisions = pointerWithin(args);
    } catch (error) {
      console.warn('Error in pointer collision detection:', error);
      pointerCollisions = [];
    }

    if (pointerCollisions.length > 0) {
      // Filter collisions but ALLOW dropping on self (for returning to original position)
      const validCollisions = pointerCollisions.filter((collision: any) => {
        try {
          const droppableId = collision?.id;
          const activeId = active?.id;

          // Validate collision object
          if (!droppableId || !activeId) {
            return false;
          }

          // ALLOW dropping on self - this enables returning to original position
          // The drag end handler will check if position actually changed

          // On mobile, require more significant movement before allowing collision
          // But only for different items, not for returning to original position
          if (isMobile && pointerCoordinates && droppableId !== activeId) {
            const activeRect = active.rect?.current?.translated;
            if (activeRect) {
              const movementThreshold = 20; // Require 20px movement
              const deltaY = Math.abs(pointerCoordinates.y - activeRect.top);
              if (deltaY < movementThreshold) {
                return false; // Not enough movement to trigger collision
              }
            }
          }

          return true;
        } catch (filterError) {
          console.warn('Error filtering collision:', filterError);
          return false;
        }
      });

      if (validCollisions.length > 0) {
        return validCollisions;
      }
    }

    // Fallback to closest corners which works better than closest center for tall items
    let cornerCollisions: any[] = [];
    try {
      cornerCollisions = closestCorners(args);
    } catch (error) {
      console.warn('Error in corner collision detection:', error);
      return []; // Return empty array if both methods fail
    }

    // Apply the same filtering to corner collisions
    return cornerCollisions.filter((collision: any) => {
      try {
        const droppableId = collision?.id;
        const activeId = active?.id;

        // Validate collision object
        if (!droppableId || !activeId) {
          return false;
        }

        // ALLOW dropping on self - this enables returning to original position

        // On mobile, require more significant movement for corner collisions too
        // But only for different items, not for returning to original position
        if (isMobile && pointerCoordinates && droppableId !== activeId) {
          const activeRect = active.rect?.current?.translated;
          if (activeRect) {
            const movementThreshold = 15; // Slightly lower threshold for corner detection
            const deltaY = Math.abs(pointerCoordinates.y - activeRect.top);
            if (deltaY < movementThreshold) {
              return false; // Not enough movement to trigger collision
            }
          }
        }

        return true;
      } catch (filterError) {
        console.warn('Error filtering corner collision:', filterError);
        return false;
      }
    });
  } catch (error) {
    console.error('Critical error in collision detection:', error);
    return []; // Return empty array to prevent crashes
  }
}

/**
 * Optimized measuring configuration to prevent position jumps and visual warping
 * Use BeforeDragging to prevent displaced elements from changing dimensions
 */
export const dragMeasuring = {
  droppable: {
    strategy: MeasuringStrategy.BeforeDragging,
  },
  draggable: {
    strategy: MeasuringStrategy.BeforeDragging,
  },
};

/**
 * Haptic feedback utilities for mobile devices
 */
export const hapticFeedback = {
  /**
   * Provide haptic feedback when drag starts
   */
  onDragStart: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  },

  /**
   * Provide haptic feedback when dragging over a new target
   */
  onDragOver: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  },
};



/**
 * Standard drag over handler with haptic feedback and error handling
 */
export function createDragOverHandler(
  lastDragOverRef?: React.MutableRefObject<string | null>
) {
  return (event: any) => {
    try {
      const { over } = event;

      // Only provide haptic feedback when moving to a new target
      if (over && lastDragOverRef && over.id !== lastDragOverRef.current) {
        lastDragOverRef.current = over.id as string;
        hapticFeedback.onDragOver();
      }
    } catch (error) {
      console.error('Error in drag over handler:', error);
      // Reset drag over tracking on error
      if (lastDragOverRef) {
        lastDragOverRef.current = null;
      }
    }
  };
}

/**
 * Standard drag end handler with state cleanup and error recovery
 */
export function createDragEndHandler(
  setIsDragging: (isDragging: boolean) => void,
  setActiveItem: (item: any) => void,
  isReorderingRef?: React.MutableRefObject<boolean>,
  lastDragOverRef?: React.MutableRefObject<string | null>
) {
  return () => {
    try {
      // Clear any existing timeout
      if (typeof window !== 'undefined' && (window as any).__dragTimeoutId) {
        clearTimeout((window as any).__dragTimeoutId);
        delete (window as any).__dragTimeoutId;
      }

      // Immediately clear drag state for instant response
      setIsDragging(false);
      setActiveItem(null);

      if (lastDragOverRef) {
        lastDragOverRef.current = null; // Reset drag over tracking
      }

      // Allow prop updates after a brief delay to prevent flicker
      if (isReorderingRef) {
        const timeoutId = setTimeout(() => {
          try {
            isReorderingRef.current = false;
          } catch (timeoutError) {
            console.error('Error resetting reordering flag:', timeoutError);
            // Force reset even if there's an error
            isReorderingRef.current = false;
          }
        }, 100);

        // Fallback timeout in case the first one fails
        setTimeout(() => {
          if (isReorderingRef && isReorderingRef.current) {
            console.warn('Fallback: Force resetting reordering flag');
            isReorderingRef.current = false;
          }
        }, 1000);
      }
    } catch (error) {
      console.error('Error in drag end cleanup:', error);
      // Comprehensive force reset on any error
      try {
        setIsDragging(false);
      } catch (e) {
        console.error('Failed to reset isDragging:', e);
      }

      try {
        setActiveItem(null);
      } catch (e) {
        console.error('Failed to reset activeItem:', e);
      }

      if (isReorderingRef) {
        try {
          isReorderingRef.current = false;
        } catch (e) {
          console.error('Failed to reset reorderingRef:', e);
        }
      }

      if (lastDragOverRef) {
        try {
          lastDragOverRef.current = null;
        } catch (e) {
          console.error('Failed to reset lastDragOverRef:', e);
        }
      }

      // Clear timeout even in error state
      if (typeof window !== 'undefined' && (window as any).__dragTimeoutId) {
        try {
          clearTimeout((window as any).__dragTimeoutId);
          delete (window as any).__dragTimeoutId;
        } catch (e) {
          console.error('Failed to clear timeout:', e);
        }
      }
    }
  };
}

/**
 * Enhanced drag start handler with error recovery and timeout protection
 */
export function createDragStartHandler(
  setIsDragging: (isDragging: boolean) => void,
  setActiveItem: (item: any) => void,
  items: any[],
  onDragStart?: () => void,
  isReorderingRef?: React.MutableRefObject<boolean>,
  lastDragOverRef?: React.MutableRefObject<string | null>
) {
  return (event: any) => {
    try {
      // Validate event and active element
      if (!event?.active?.id) {
        console.warn('Invalid drag start event - missing active element');
        return;
      }



      setIsDragging(true);

      if (isReorderingRef) {
        isReorderingRef.current = true; // Prevent prop updates during drag
      }

      if (lastDragOverRef) {
        lastDragOverRef.current = null; // Reset drag over tracking
      }

      // Find and set the active item for the drag overlay with validation
      const draggedItem = items.find(item => item?.id === event.active.id);
      if (!draggedItem) {
        console.warn('Dragged item not found in items array:', event.active.id);
        // Continue with null - the overlay will handle this gracefully
      }
      setActiveItem(draggedItem || null);

      // Call the onDragStart callback for auto-collapse functionality
      try {
        onDragStart?.();
      } catch (callbackError) {
        console.error('Error in drag start callback:', callbackError);
        // Continue with drag operation even if callback fails
      }

      // Add haptic feedback on mobile if available
      try {
        hapticFeedback.onDragStart();
      } catch (hapticError) {
        console.error('Error providing haptic feedback:', hapticError);
        // Continue without haptic feedback
      }

      // Safety timeout to prevent stuck drag state with enhanced recovery
      const timeoutId = setTimeout(() => {
        if (isReorderingRef && isReorderingRef.current) {
          console.warn('Drag operation timeout - performing emergency reset');
          try {
            isReorderingRef.current = false;
            setIsDragging(false);
            setActiveItem(null);
            if (lastDragOverRef) {
              lastDragOverRef.current = null;
            }
          } catch (resetError) {
            console.error('Error during emergency reset:', resetError);
          }
        }
      }, 10000); // 10 second timeout

      // Store timeout ID for potential cleanup
      if (typeof window !== 'undefined') {
        (window as any).__dragTimeoutId = timeoutId;
      }
    } catch (error) {
      console.error('Critical error in drag start:', error);
      // Comprehensive state reset on any error
      try {
        setIsDragging(false);
        setActiveItem(null);
        if (isReorderingRef) {
          isReorderingRef.current = false;
        }
        if (lastDragOverRef) {
          lastDragOverRef.current = null;
        }
      } catch (resetError) {
        console.error('Error during error recovery:', resetError);
      }
    }
  };
}
