import { getUserUsage, canCreateResource, type UserUsage } from './db';
import { USAGE_LIMITS } from './limits';

export interface UsageInfo {
  current: UserUsage;
  limits: typeof USAGE_LIMITS;
  percentages: {
    spaces: number;
    lists: number;
    tasks: number;
  };
  canCreate: {
    space: boolean;
    list: boolean;
    task: boolean;
  };
}

/**
 * Get comprehensive usage information for a user
 */
export async function getUserUsageInfo(userId: string): Promise<UsageInfo> {
  const current = await getUserUsage(userId);
  
  const percentages = {
    spaces: Math.round((current.spaces / USAGE_LIMITS.SPACES) * 100),
    lists: Math.round((current.lists / USAGE_LIMITS.LISTS) * 100),
    tasks: Math.round((current.tasks / USAGE_LIMITS.TASKS) * 100),
  };

  const canCreate = {
    space: await canCreateResource(userId, 'space'),
    list: await canCreateResource(userId, 'list'),
    task: await canCreateResource(userId, 'task'),
  };

  return {
    current,
    limits: USAGE_LIMITS,
    percentages,
    canCreate,
  };
}

/**
 * Check if user has reached or exceeded a specific limit
 */
export async function hasReachedLimit(userId: string, resourceType: 'space' | 'list' | 'task'): Promise<boolean> {
  const canCreate = await canCreateResource(userId, resourceType);
  return !canCreate;
}



/**
 * Validate if a resource can be created and return appropriate error if not
 */
export async function validateResourceCreation(
  userId: string,
  resourceType: 'space' | 'list' | 'task'
): Promise<{ canCreate: boolean; resourceType?: 'space' | 'list' | 'task' }> {
  const canCreate = await canCreateResource(userId, resourceType);

  if (!canCreate) {
    return {
      canCreate: false,
      resourceType,
    };
  }

  return { canCreate: true };
}
