/**
 * Comprehensive error handling and user feedback for drag and drop operations
 */

export interface DragError {
  type: 'validation' | 'network' | 'timeout' | 'unknown';
  message: string;
  details?: any;
  recoverable: boolean;
}

export interface DragErrorHandler {
  onError: (error: DragError) => void;
  onRecovery: () => void;
  showUserFeedback: (message: string, type: 'error' | 'warning' | 'info') => void;
}

/**
 * Create a standardized error for drag operations
 */
export function createDragError(
  type: DragError['type'],
  message: string,
  details?: any,
  recoverable: boolean = true
): DragError {
  return {
    type,
    message,
    details,
    recoverable
  };
}

/**
 * Enhanced error handling for drag operations with user feedback
 */
export function createDragErrorHandler(
  showUserFeedback: (message: string, type: 'error' | 'warning' | 'info') => void
): DragErrorHandler {
  const onError = (error: DragError) => {
    console.error('Drag operation error:', error);
    
    // Provide user-friendly feedback based on error type
    switch (error.type) {
      case 'validation':
        showUserFeedback(error.message, 'warning');
        break;
      case 'network':
        showUserFeedback('Network error: Changes could not be saved. Please try again.', 'error');
        break;
      case 'timeout':
        showUserFeedback('Operation timed out. Please try again.', 'warning');
        break;
      default:
        showUserFeedback('An unexpected error occurred. Please try again.', 'error');
    }
  };

  const onRecovery = () => {
    showUserFeedback('Operation completed successfully.', 'info');
  };

  return {
    onError,
    onRecovery,
    showUserFeedback
  };
}

/**
 * Validation errors for drag operations
 */
export const DragValidationErrors = {
  INVALID_TARGET: createDragError(
    'validation',
    'Cannot move item to this location',
    null,
    true
  ),
  SELF_DROP: createDragError(
    'validation',
    'Cannot drop item on itself',
    null,
    true
  ),
  PARENT_CHILD_CONFLICT: createDragError(
    'validation',
    'Cannot move a task into its own subtask',
    null,
    true
  ),
  MISSING_ITEM: createDragError(
    'validation',
    'Item not found in current list',
    null,
    false
  ),
  INVALID_INDICES: createDragError(
    'validation',
    'Invalid position for drag operation',
    null,
    true
  )
};

/**
 * Network errors for drag operations
 */
export const DragNetworkErrors = {
  SAVE_FAILED: createDragError(
    'network',
    'Failed to save changes to server',
    null,
    true
  ),
  CONNECTION_LOST: createDragError(
    'network',
    'Connection lost during operation',
    null,
    true
  ),
  SERVER_ERROR: createDragError(
    'network',
    'Server error occurred',
    null,
    true
  )
};

/**
 * Timeout errors for drag operations
 */
export const DragTimeoutErrors = {
  OPERATION_TIMEOUT: createDragError(
    'timeout',
    'Drag operation took too long',
    null,
    true
  ),
  SAVE_TIMEOUT: createDragError(
    'timeout',
    'Save operation timed out',
    null,
    true
  )
};

/**
 * Recovery strategies for different error types
 */
export const RecoveryStrategies = {
  validation: {
    autoRetry: false,
    userAction: 'Try a different position',
    resetState: true
  },
  network: {
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    userAction: 'Check connection and retry',
    resetState: false
  },
  timeout: {
    autoRetry: true,
    maxRetries: 2,
    retryDelay: 2000,
    userAction: 'Try again',
    resetState: true
  },
  unknown: {
    autoRetry: false,
    userAction: 'Refresh page if problem persists',
    resetState: true
  }
};

/**
 * Enhanced drag operation wrapper with comprehensive error handling
 */
export async function withDragErrorHandling<T>(
  operation: () => Promise<T>,
  errorHandler: DragErrorHandler,
  operationName: string = 'drag operation'
): Promise<T | null> {
  try {
    const result = await operation();
    return result;
  } catch (error) {
    let dragError: DragError;

    if (error instanceof Error) {
      // Categorize the error based on its properties
      if (error.message.includes('network') || error.message.includes('fetch')) {
        dragError = createDragError('network', error.message, error, true);
      } else if (error.message.includes('timeout')) {
        dragError = createDragError('timeout', error.message, error, true);
      } else if (error.message.includes('validation') || error.message.includes('invalid')) {
        dragError = createDragError('validation', error.message, error, true);
      } else {
        dragError = createDragError('unknown', `${operationName} failed: ${error.message}`, error, true);
      }
    } else {
      dragError = createDragError('unknown', `${operationName} failed with unknown error`, error, false);
    }

    errorHandler.onError(dragError);
    return null;
  }
}

/**
 * Retry mechanism with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      if (attempt === maxRetries) {
        throw lastError;
      }

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}
