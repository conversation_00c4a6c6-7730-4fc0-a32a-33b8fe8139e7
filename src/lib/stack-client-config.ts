"use client";

/**
 * This file contains client-side configuration for Stack Auth
 * It helps bypass CORS issues in development
 */

// Check if we're in development mode
const isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';

/**
 * Get the base URL for Stack Auth API calls
 * In development, we use our proxy API route
 * In production, we use the default Stack Auth API
 */
export function getStackApiBaseUrl(): string | undefined {
  // We'll use the default Stack Auth API directly
  // Our proxy approach isn't working well with the Stack Auth library
  return undefined;
}

/**
 * Transform a Stack Auth API URL to use our proxy in development
 * @param url The original Stack Auth API URL
 * @returns The transformed URL
 */
export function transformStackApiUrl(url: string): string {
  if (!isDevelopment) {
    return url;
  }

  // If the URL is already a relative URL (our proxy), return it as is
  if (url.startsWith("/api/auth-proxy")) {
    return url;
  }

  // If the URL is an absolute URL to the Stack Auth API, transform it to use our proxy
  if (url.startsWith("https://api.stack-auth.com")) {
    // Extract the path from the URL
    const path = url.substring("https://api.stack-auth.com".length);

    // Create the proxy URL
    return `/api/auth-proxy?url=https://api.stack-auth.com&path=${encodeURIComponent(path)}`;
  }

  // Otherwise, return the URL as is
  return url;
}

/**
 * Monkey patch the fetch function to intercept Stack Auth API calls
 * This is a more aggressive approach to fix CORS issues
 */
export function setupFetchInterceptor() {
  if (!isDevelopment || typeof window === 'undefined') {
    return;
  }

  console.log("Setting up fetch interceptor for Stack Auth API");

  // Store the original fetch function
  const originalFetch = window.fetch;

  // Replace the fetch function with our interceptor
  window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
    // Convert input to string
    const url = input instanceof Request ? input.url : input.toString();

    // Check if this is a Stack Auth API call
    if (url.includes('api.stack-auth.com')) {
      console.log("Intercepting Stack Auth API call:", url);

      // Transform the URL to use our proxy
      const transformedUrl = transformStackApiUrl(url);
      console.log("Transformed URL:", transformedUrl);

      // Call the original fetch with the transformed URL
      return originalFetch(transformedUrl, init);
    }

    // Otherwise, call the original fetch
    return originalFetch(input, init);
  };
}
