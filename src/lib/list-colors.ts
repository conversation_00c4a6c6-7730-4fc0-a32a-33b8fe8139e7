// Predefined neon rainbow colors for lists (ordered by ROYGBIV spectrum)
export const LIST_COLORS = [
  { name: 'Neon Red', value: '#ff073a', class: 'bg-[#ff073a]' },
  { name: 'Neon Coral', value: '#ff3366', class: 'bg-[#ff3366]' },
  { name: 'Neon Rose', value: '#ff0066', class: 'bg-[#ff0066]' },
  { name: 'Neon Orange', value: '#ff6600', class: 'bg-[#ff6600]' },
  { name: 'Neon Yellow', value: '#ffff00', class: 'bg-[#ffff00]' },
  { name: 'Neon Lime', value: '#ccff00', class: 'bg-[#ccff00]' },
  { name: 'Neon Green', value: '#00ff00', class: 'bg-[#00ff00]' },
  { name: 'Neon Mint', value: '#00ff99', class: 'bg-[#00ff99]' },
  { name: 'Neon Cyan', value: '#00ffff', class: 'bg-[#00ffff]' },
  { name: 'Neon Sky', value: '#0099ff', class: 'bg-[#0099ff]' },
  { name: 'Neon Blue', value: '#0066ff', class: 'bg-[#0066ff]' },
  { name: 'Neon Indigo', value: '#3300ff', class: 'bg-[#3300ff]' },
  { name: 'Neon Violet', value: '#9933ff', class: 'bg-[#9933ff]' },
  { name: 'Neon Purple', value: '#6600ff', class: 'bg-[#6600ff]' },
  { name: 'Neon Magenta', value: '#cc00ff', class: 'bg-[#cc00ff]' },
  { name: 'Neon Pink', value: '#ff00cc', class: 'bg-[#ff00cc]' },
] as const;

// Colorless option (represented as null in database)
export const COLORLESS_OPTION = {
  name: 'Colorless',
  value: null,
  class: 'bg-muted',
  displayColor: '#f5f5f5', // Muted neon white for visual representation
} as const;

// All available list colors including colorless (for backward compatibility)
export const ALL_LIST_COLORS = [COLORLESS_OPTION, ...LIST_COLORS] as const;

// List colors for picker (excluding colorless option)
export const PICKER_LIST_COLORS = LIST_COLORS;

export type ListColor = typeof LIST_COLORS[number];
export type ListColorOption = typeof ALL_LIST_COLORS[number];

export function getListColorByValue(value: string | null): ListColorOption | undefined {
  if (value === null) {
    return COLORLESS_OPTION;
  }
  return LIST_COLORS.find(color => color.value === value);
}

export function getRandomListColor(): ListColor {
  return LIST_COLORS[Math.floor(Math.random() * LIST_COLORS.length)];
}

export function getContrastTextColor(backgroundColor: string | null): string {
  if (!backgroundColor) {
    // For colorless lists, use default text color
    return 'var(--foreground)';
  }

  // Convert hex to RGB
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

/**
 * Get CSS styles for list color theming
 */
export function getListColorStyles(color: string | null) {
  if (!color) {
    return {
      backgroundColor: 'var(--muted)',
      borderColor: 'var(--border)',
      textColor: '#4b5563', // Much brighter color for better visibility
    };
  }

  return {
    backgroundColor: color,
    borderColor: color,
    textColor: getContrastTextColor(color),
  };
}

/**
 * Get Tailwind classes for list color theming
 */
export function getListColorClasses(color: string | null, variant: 'background' | 'border' | 'text' = 'background') {
  if (!color) {
    switch (variant) {
      case 'background':
        return 'bg-muted';
      case 'border':
        return 'border-border';
      case 'text':
        return 'text-muted-foreground';
      default:
        return 'bg-muted';
    }
  }

  const colorValue = color.replace('#', '');
  switch (variant) {
    case 'background':
      return `bg-[${color}]`;
    case 'border':
      return `border-[${color}]`;
    case 'text':
      return getContrastTextColor(color) === '#ffffff' ? 'text-white' : 'text-black';
    default:
      return `bg-[${color}]`;
  }
}

/**
 * Check if a color is considered "light" for accessibility
 */
export function isLightColor(color: string | null): boolean {
  if (!color) return true; // Colorless is considered light

  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5;
}
