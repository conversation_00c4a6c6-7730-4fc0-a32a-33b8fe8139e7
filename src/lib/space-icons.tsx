// Use Lucide React icons - much faster build times than Tabler
import {
  Briefcase,
  Clipboard,
  Folder,
  FileText,
  BarChart3,
  Target,
  Lightbulb,
  Rocket,
  Palette,
  Brush,
  Camera,
  Music,
  Headphones,
  Mic,
  Video,
  Image,
  Pencil,
  Sparkles,
  Home,
  Heart,
  User,
  Users,
  Coffee,
  Pizza,
  ChefHat,
  Book,
  BookOpen,
  Plane,
  Car,
  Bike,
  Map,
  Compass,
  Mountain,
  Tent,
  Backpack,
  Globe,
  MapPin,
  Activity,
  Apple,
  GraduationCap,
  Award,
  Trophy,
  Medal,
  Star,
  Flame,
  TrendingUp,
  Gamepad2,
  Puzzle,
  Dice1,
  Gift,
  Cake,
  Trees,
  Leaf,
  Flower,
  Sun,
  Moon,
  Cloud,
  Snowflake,
} from "lucide-react";

// Lucide icons for spaces - curated selection for personal expression
export const SPACE_ICONS = [
  // Work & Productivity
  "briefcase", "clipboard", "folder", "file-text", "chart-bar", "target", "bulb", "rocket",

  // Creative & Hobbies
  "palette", "brush", "camera", "music", "headphones", "microphone", "video", "photo",
  "pencil", "sparkles",

  // Home & Personal
  "home", "heart", "user", "users", "coffee", "pizza", "chef-hat", "book", "books",

  // Travel & Adventure
  "plane", "car", "bike", "map", "compass", "mountain", "tent", "backpack", "globe", "map-pin",

  // Health & Fitness
  "activity", "apple",

  // Learning & Growth
  "graduation-cap", "award", "trophy", "medal", "star", "flame", "trending-up",

  // Fun & Entertainment
  "gamepad", "puzzle", "dice", "gift", "cake",

  // Nature & Outdoors
  "trees", "leaf", "flower", "sun", "moon", "cloud", "snowflake"
];

// Icon mapping for Lucide icons
const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  // Work & Productivity
  'briefcase': Briefcase,
  'clipboard': Clipboard,
  'folder': Folder,
  'file-text': FileText,
  'chart-bar': BarChart3,
  'target': Target,
  'bulb': Lightbulb,
  'rocket': Rocket,

  // Creative & Hobbies
  'palette': Palette,
  'brush': Brush,
  'camera': Camera,
  'music': Music,
  'headphones': Headphones,
  'microphone': Mic,
  'video': Video,
  'photo': Image,
  'pencil': Pencil,
  'sparkles': Sparkles,

  // Home & Personal
  'home': Home,
  'heart': Heart,
  'user': User,
  'users': Users,
  'coffee': Coffee,
  'pizza': Pizza,
  'chef-hat': ChefHat,
  'book': Book,
  'books': BookOpen,

  // Travel & Adventure
  'plane': Plane,
  'car': Car,
  'bike': Bike,
  'map': Map,
  'compass': Compass,
  'mountain': Mountain,
  'tent': Tent,
  'backpack': Backpack,
  'globe': Globe,
  'map-pin': MapPin,

  // Health & Fitness
  'activity': Activity,
  'apple': Apple,

  // Learning & Growth
  'graduation-cap': GraduationCap,
  'award': Award,
  'trophy': Trophy,
  'medal': Medal,
  'star': Star,
  'flame': Flame,
  'trending-up': TrendingUp,

  // Fun & Entertainment
  'gamepad': Gamepad2,
  'puzzle': Puzzle,
  'dice': Dice1,
  'gift': Gift,
  'cake': Cake,

  // Nature & Outdoors
  'trees': Trees,
  'leaf': Leaf,
  'flower': Flower,
  'sun': Sun,
  'moon': Moon,
  'cloud': Cloud,
  'snowflake': Snowflake,
};

// Helper function to render Lucide icons
export const renderSpaceIcon = (iconName: string, className?: string) => {
  // Skip emoji icons (they contain non-ASCII characters)
  if (iconName && /[^\x00-\x7F]/.test(iconName)) {
    return <Clipboard className={className || "h-5 w-5"} />;
  }

  const IconComponent = iconMap[iconName];

  if (IconComponent) {
    return <IconComponent className={className || "h-5 w-5"} />;
  }

  // Fallback to clipboard icon if not found
  return <Clipboard className={className || "h-5 w-5"} />;
};
