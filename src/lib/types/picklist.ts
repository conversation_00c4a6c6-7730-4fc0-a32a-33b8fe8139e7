import type { Tag, PicklistValue, TaskPicklistSelection } from '@/lib/db';

// Tag type enum
export type TagType = 'regular' | 'picklist';

// Extended tag interface that includes type information
export interface ExtendedTag extends Tag {
  type: TagType;
}

// Picklist tag interface - a tag that has predefined values
export interface PicklistTag extends ExtendedTag {
  type: 'picklist';
  picklistValues?: PicklistValue[];
}

// Regular tag interface - standard tag without predefined values
export interface RegularTag extends ExtendedTag {
  type: 'regular';
}

// Union type for all tag types
export type AnyTag = RegularTag | PicklistTag;

// Picklist value with additional metadata
export interface ExtendedPicklistValue extends PicklistValue {
  tag?: PicklistTag;
}

// Task picklist selection with additional metadata
export interface ExtendedTaskPicklistSelection extends TaskPicklistSelection {
  picklistValue?: ExtendedPicklistValue;
  tag?: PicklistTag;
}

// Interface for creating a new picklist tag
export interface CreatePicklistTagData {
  name: string;
  color: string;
  type: 'picklist';
  initialValues: string[];
}

// Interface for creating a regular tag
export interface CreateRegularTagData {
  name: string;
  color: string;
  type: 'regular';
}

// Union type for creating any tag type
export type CreateTagData = CreateRegularTagData | CreatePicklistTagData;

// Interface for updating picklist values
export interface UpdatePicklistValuesData {
  tagId: string;
  values: Array<{
    id?: string; // undefined for new values
    value: string;
    position: number;
  }>;
}

// Interface for selecting a picklist value for a task
export interface SelectPicklistValueData {
  taskId: string;
  tagId: string;
  picklistValueId: string;
}

// Interface for picklist tag with selected value (used in UI)
export interface PicklistTagWithSelection extends PicklistTag {
  selectedValue?: ExtendedPicklistValue;
}

// Type guard functions
export function isPicklistTag(tag: AnyTag): tag is PicklistTag {
  return tag.type === 'picklist';
}

export function isRegularTag(tag: AnyTag): tag is RegularTag {
  return tag.type === 'regular';
}

// Helper function to convert database Tag to ExtendedTag
export function toExtendedTag(tag: Tag): ExtendedTag {
  return {
    ...tag,
    type: (tag.type as TagType) || 'regular', // fallback for existing tags
  };
}

// Helper function to convert ExtendedTag to specific tag type
export function toPicklistTag(tag: ExtendedTag, picklistValues?: PicklistValue[]): PicklistTag | null {
  if (tag.type !== 'picklist') return null;
  return {
    ...tag,
    type: 'picklist',
    picklistValues,
  };
}

export function toRegularTag(tag: ExtendedTag): RegularTag | null {
  if (tag.type !== 'regular') return null;
  return {
    ...tag,
    type: 'regular',
  };
}
