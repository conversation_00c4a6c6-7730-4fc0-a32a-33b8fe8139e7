import { USAGE_LIMITS } from './limits';

/**
 * Get usage limit error message for a specific resource type
 * This is client-safe and doesn't import any server-only code
 */
export function getUsageLimitMessage(resourceType: 'space' | 'list' | 'task'): string {
  switch (resourceType) {
    case 'space':
      return `You've reached the limit of ${USAGE_LIMITS.SPACES} spaces. Delete some old spaces or upgrade to create more.`;
    case 'list':
      return `You've reached the limit of ${USAGE_LIMITS.LISTS} lists. Delete some old lists or upgrade to create more.`;
    case 'task':
      return `You've reached the limit of ${USAGE_LIMITS.TASKS} tasks. Delete some old tasks or upgrade to create more.`;
    default:
      return 'You have reached your usage limit. Please delete some old data or upgrade.';
  }
}
