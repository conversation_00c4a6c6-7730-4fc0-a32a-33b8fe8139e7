import { createUser, getUserById, updateUser } from "./db";
import type { User as StackUser } from "@stackframe/stack";

export async function syncUserWithDatabase(user: StackUser) {
  try {
    // Check if user exists in our database
    const existingUser = await getUserById(user.id);
    
    if (!existingUser) {
      // Create new user in our database
      await createUser({
        id: user.id,
        email: user.primaryEmail || "",
        name: user.displayName || null,
        avatar_url: user.avatarUrl || null,
      });
    } else {
      // Update existing user if needed
      if (
        existingUser.name !== user.displayName ||
        existingUser.avatar_url !== user.avatarUrl
      ) {
        await updateUser(user.id, {
          name: user.displayName || null,
          avatar_url: user.avatarUrl || null,
        });
      }
    }
  } catch (error) {
    console.error("Error syncing user with database:", error);
  }
}
