import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Aggressive staleTime for different data types
      staleTime: (query) => {
        const queryKey = query.queryKey[0] as string;

        // Static data that rarely changes - 30 minutes
        if (['tags', 'userSettings'].includes(queryKey)) {
          return 30 * 60 * 1000; // 30 minutes
        }

        // Lists change infrequently - 20 minutes
        if (['lists', 'defaultList', 'taskCounts'].includes(queryKey)) {
          return 20 * 60 * 1000; // 20 minutes
        }

        // Tasks change more frequently - 15 minutes
        if (['tasks', 'taskTags'].includes(queryKey)) {
          return 15 * 60 * 1000; // 15 minutes
        }

        // Search results are temporary - 5 minutes
        if (queryKey === 'searchTags') {
          return 5 * 60 * 1000; // 5 minutes
        }

        // Default for other queries - 10 minutes
        return 10 * 60 * 1000; // 10 minutes
      },
      // Extended cache time for better persistence - 60 minutes
      gcTime: 60 * 60 * 1000, // 60 minutes
      // Retry failed requests
      retry: 1,
      // Disable refetch on window focus to prevent unnecessary requests
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect to avoid unnecessary requests
      refetchOnReconnect: false,
      // Add network mode for better loading states
      networkMode: 'online',
      // Reduce retry delay for faster feedback
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Enable background refetching only for stale data
      refetchOnMount: 'always',
      // Use cached data while refetching in background
      notifyOnChangeProps: 'all',
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
      // Add network mode for mutations
      networkMode: 'online',
      // Reduce retry delay for mutations
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});
