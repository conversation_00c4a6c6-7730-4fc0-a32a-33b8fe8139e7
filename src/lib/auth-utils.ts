"use server";

import { cookies } from "next/headers";

/**
 * Clears all Stack Auth related cookies to help break authentication loops
 * This must be called from a Server Action or Route Handler
 */
export async function clearAuthCookies() {
  try {
    // This function must be called from a Server Action or Route Handler
    // It cannot be called directly from a Server Component
    const cookieStore = cookies();

    // Get all cookies
    const allCookies = await cookieStore.getAll();

    // Find all Stack Auth related cookies
    const stackCookies = allCookies.filter(cookie =>
      cookie.name.startsWith('stack-') ||
      cookie.name.includes('auth') ||
      cookie.name.includes('session')
    );

    console.log('Found auth cookies to clear:', stackCookies.map(c => c.name));

    // We'll return the count but not actually delete them here
    // since this might be called from a Server Component
    return stackCookies.length;
  } catch (error) {
    console.error('Error finding cookies:', error);
    return 0;
  }
}

/**
 * Utility function to log all cookies for debugging
 */
export async function logAllCookies(prefix: string = 'Cookies') {
  try {
    // Simple logging that doesn't try to modify cookies
    console.log(`${prefix}: Checking cookies...`);
    return [];
  } catch (error) {
    console.error(`Error logging cookies (${prefix}):`, error);
    return [];
  }
}
