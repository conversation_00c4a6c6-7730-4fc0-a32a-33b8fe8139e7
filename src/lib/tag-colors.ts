// Predefined neon rainbow colors for tags (ordered by ROYGBIV spectrum)
export const TAG_COLORS = [
  { name: 'Neon Red', value: '#ff073a', class: 'bg-[#ff073a]' },
  { name: 'Neon Coral', value: '#ff3366', class: 'bg-[#ff3366]' },
  { name: 'Neon Rose', value: '#ff0066', class: 'bg-[#ff0066]' },
  { name: 'Neon Orange', value: '#ff6600', class: 'bg-[#ff6600]' },
  { name: 'Neon Yellow', value: '#ffff00', class: 'bg-[#ffff00]' },
  { name: 'Neon Lime', value: '#ccff00', class: 'bg-[#ccff00]' },
  { name: 'Neon Green', value: '#00ff00', class: 'bg-[#00ff00]' },
  { name: 'Neon Mint', value: '#00ff99', class: 'bg-[#00ff99]' },
  { name: 'Neon Cyan', value: '#00ffff', class: 'bg-[#00ffff]' },
  { name: 'Neon Sky', value: '#0099ff', class: 'bg-[#0099ff]' },
  { name: 'Neon Blue', value: '#0066ff', class: 'bg-[#0066ff]' },
  { name: 'Neon Indigo', value: '#3300ff', class: 'bg-[#3300ff]' },
  { name: 'Neon Violet', value: '#9933ff', class: 'bg-[#9933ff]' },
  { name: 'Neon Purple', value: '#6600ff', class: 'bg-[#6600ff]' },
  { name: 'Neon Magenta', value: '#cc00ff', class: 'bg-[#cc00ff]' },
  { name: 'Neon Pink', value: '#ff00cc', class: 'bg-[#ff00cc]' },
] as const;

export type TagColor = typeof TAG_COLORS[number];

export function getTagColorByValue(value: string): TagColor | undefined {
  return TAG_COLORS.find(color => color.value === value);
}

export function getRandomTagColor(): TagColor {
  return TAG_COLORS[Math.floor(Math.random() * TAG_COLORS.length)];
}

export function getContrastTextColor(backgroundColor: string): string {
  // Convert hex to RGB
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
}
