import { QueryClient } from '@tanstack/react-query';

// Simple localStorage-based persistence without external dependencies
const CACHE_KEY = 'neon-tasks-cache';
const CACHE_VERSION = '1.0';

interface CacheData {
  version: string;
  timestamp: number;
  queries: any[];
}

// Create a simple persister using localStorage
function createPersister() {
  if (typeof window === 'undefined') {
    return null;
  }

  return {
    persistClient: async (client: any) => {
      try {
        const queryCache = client.getQueryCache();
        const queries = queryCache.getAll();

        // Only persist specific query types that are safe to persist
        const persistableQueries = queries.filter((query: any) => {
          const queryKey = query.queryKey[0] as string;
          const persistableTypes = [
            'lists',
            'defaultList',
            'taskCounts',
            'tasks',
            'tags',
            'taskTags',
            'bulkTaskTags',
            'userSettings'
          ];

          return persistableTypes.includes(queryKey) && query.state.data;
        });

        const cacheData: CacheData = {
          version: CACHE_VERSION,
          timestamp: Date.now(),
          queries: persistableQueries.map((query: any) => ({
            queryKey: query.queryKey,
            queryHash: query.queryHash,
            state: {
              data: query.state.data,
              dataUpdatedAt: query.state.dataUpdatedAt,
              status: query.state.status,
            }
          }))
        };

        localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      } catch (error) {
        console.warn('Failed to persist cache:', error);
      }
    },

    restoreClient: async (client: any) => {
      try {
        const cached = localStorage.getItem(CACHE_KEY);
        if (!cached) return;

        const cacheData: CacheData = JSON.parse(cached);

        // Check cache version and age (24 hours max)
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        const age = Date.now() - cacheData.timestamp;

        if (cacheData.version !== CACHE_VERSION || age > maxAge) {
          localStorage.removeItem(CACHE_KEY);
          return;
        }

        // Restore queries to cache
        const queryCache = client.getQueryCache();

        cacheData.queries.forEach((queryData: any) => {
          try {
            queryCache.build(client, {
              queryKey: queryData.queryKey,
              queryHash: queryData.queryHash,
            }).setState({
              ...queryData.state,
              // Mark all restored data as stale to trigger background refetch
              dataUpdatedAt: queryData.state.dataUpdatedAt,
              status: 'success',
            });
          } catch (error) {
            console.warn('Failed to restore query:', queryData.queryKey, error);
          }
        });

        console.log(`🔄 Restored ${cacheData.queries.length} queries from cache`);
      } catch (error) {
        console.warn('Failed to restore cache:', error);
        localStorage.removeItem(CACHE_KEY);
      }
    }
  };
}

// Configure cache persistence
export async function setupCachePersistence(queryClient: QueryClient) {
  if (typeof window === 'undefined') {
    return;
  }

  const persister = createPersister();
  if (!persister) {
    return;
  }

  try {
    // Restore cache on initialization
    await persister.restoreClient(queryClient);

    // Set up periodic persistence (every 30 seconds)
    const persistInterval = setInterval(() => {
      persister.persistClient(queryClient);
    }, 30000);

    // Persist on page unload
    const handleBeforeUnload = () => {
      persister.persistClient(queryClient);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function
    return () => {
      clearInterval(persistInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  } catch (error) {
    console.warn('Failed to setup cache persistence:', error);
  }
}

// Utility to clear cache (useful for logout or data corruption)
export function clearPersistedCache() {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(CACHE_KEY);
    console.log('🧹 Persisted cache cleared');
  } catch (error) {
    console.warn('Failed to clear cache:', error);
  }
}

// Utility to check if cache exists
export function hasCachedData(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (!cached) return false;

    const cacheData: CacheData = JSON.parse(cached);

    // Check if cache is valid (not expired and correct version)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const age = Date.now() - cacheData.timestamp;

    return cacheData.version === CACHE_VERSION && age <= maxAge && cacheData.queries.length > 0;
  } catch {
    return false;
  }
}

// Utility to get cache info
export function getCacheInfo() {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const cached = localStorage.getItem(CACHE_KEY);
    if (!cached) return null;

    const cacheData: CacheData = JSON.parse(cached);
    const age = Date.now() - cacheData.timestamp;

    return {
      version: cacheData.version,
      timestamp: cacheData.timestamp,
      age: Math.round(age / 1000 / 60), // age in minutes
      queryCount: cacheData.queries.length,
      sizeKB: Math.round(new Blob([cached]).size / 1024)
    };
  } catch {
    return null;
  }
}
