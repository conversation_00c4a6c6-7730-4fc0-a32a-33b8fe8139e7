import { sql } from 'drizzle-orm';
import { getJWTAuthenticatedDb } from './db/index';
import { stackServerApp } from '@/stack';

/**
 * Execute a database operation with user context set for RLS using JWT claims
 * This is the main function for authenticated database operations
 */
export async function withUserContext<T>(
  userId: string,
  operation: () => Promise<T>,
  email?: string
): Promise<T> {
  try {
    const authDb = getJWTAuthenticatedDb();

    // Create JWT claims with the user ID (and email if available) for RLS functions
    const jwtClaims = JSON.stringify(email ? { sub: userId, email } : { sub: userId });
    await authDb.execute(sql`SELECT set_config('request.jwt.claims', ${jwtClaims}, true)`);

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔐 User context set to: ${userId}`);
    }

    return await operation();
  } catch (error) {
    console.error('Error in withUserContext:', error);
    throw error;
  }
}

/**
 * Get authenticated user from <PERSON>ack Auth and execute operation with RLS
 * This is the recommended way to handle authenticated operations
 */
export async function withStackAuthUser<T>(
  operation: (userId: string) => Promise<T>
): Promise<T> {
  const user = await stackServerApp.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }

  return withUserContext(user.id, () => operation(user.id), user.primaryEmail || undefined);
}
