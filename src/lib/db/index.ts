import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import * as schema from './schema';

// Create connection pools
const adminPool = new Pool({ connectionString: process.env.DATABASE_URL! });
const authenticatedPool = new Pool({ connectionString: process.env.DATABASE_URL! });

// Create Drizzle instances
export const db = drizzle(adminPool, { schema }); // For admin operations (migrations, etc.)
export const authenticatedDb = drizzle(authenticatedPool, { schema }); // For user operations with RLS

/**
 * Get the authenticated database connection for RLS operations
 */
export function getJWTAuthenticatedDb() {
  return authenticatedDb;
}

// Export the schema for use in other files
export * from './schema';
