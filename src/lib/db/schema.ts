import { pgTable, text, timestamp, boolean, integer } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { crudPolicy, authenticatedRole, authUid } from 'drizzle-orm/neon';

// Users table
export const users = pgTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name'),
  avatar_url: text('avatar_url'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: authUid(table.id),
    modify: authUid(table.id),
  }),
]);

// User settings table
export const userSettings = pgTable('user_settings', {
  user_id: text('user_id').primaryKey().references(() => users.id, { onDelete: 'cascade' }),
  theme: text('theme').default('system').notNull(),
  notifications_enabled: boolean('notifications_enabled').default(true).notNull(),
  week_starts_on: text('week_starts_on').default('monday').notNull(),
  mascot: text('mascot').default('golden').notNull(),
  primary_color: text('primary_color'), // Nullable - null means colorless/default
  default_space_id: text('default_space_id'), // Nullable - references the user's default space
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: authUid(table.user_id),
    modify: authUid(table.user_id),
  }),
]);

// Spaces table
export const spaces = pgTable('spaces', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  description: text('description'), // Nullable description field for space details
  icon: text('icon'), // Nullable icon field for space theming
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      auth.user_id() = ${table.user_id}
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = ${table.id}
          AND sc.user_id = auth.user_id()
      )
    )`,
    // Only the owner can modify the space itself
    modify: sql`(
      auth.user_id() = ${table.user_id}
    )`,
  }),
]);

// Lists table
export const lists = pgTable('lists', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  space_id: text('space_id').notNull().references(() => spaces.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  description: text('description'), // Nullable description field for list details
  color: text('color'), // Nullable color field for list theming
  icon: text('icon'), // Nullable icon field for list theming (mirrors spaces.icon)
  position: integer('position').notNull().default(0),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      ${table.user_id} = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = ${table.space_id}
          AND sc.user_id = auth.user_id()
      )
    )`,
    modify: sql`(
      ${table.user_id} = auth.user_id()
      OR EXISTS (
        SELECT 1 FROM space_collaborators sc
        WHERE sc.space_id = ${table.space_id}
          AND sc.user_id = auth.user_id()
      )
    )`,
  }),
]);

// Tasks table
export const tasks = pgTable('tasks', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  list_id: text('list_id').notNull().references(() => lists.id, { onDelete: 'cascade' }),
  parent_task_id: text('parent_task_id'),
  title: text('title').notNull(),
  description: text('description'),
  due_date: timestamp('due_date'),
  status: text('status').default('not_started').notNull(),
  position: integer('position').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      ${table.user_id} = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = ${table.list_id}
          AND sc.user_id = auth.user_id()
      )
    )`,
    modify: sql`(
      ${table.user_id} = auth.user_id()
      OR EXISTS (
        SELECT 1
        FROM lists l
        JOIN space_collaborators sc ON sc.space_id = l.space_id
        WHERE l.id = ${table.list_id}
          AND sc.user_id = auth.user_id()
      )
    )`,
  }),
]);

// Task activities table
export const taskActivities = pgTable('task_activities', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  task_id: text('task_id').notNull().references(() => tasks.id, { onDelete: 'cascade' }),
  activity_type: text('activity_type').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: authUid(table.user_id),
    modify: authUid(table.user_id),
  }),
]);

// Space Collaborators table
export const spaceCollaborators = pgTable('space_collaborators', {
  id: text('id').primaryKey(),
  space_id: text('space_id').notNull().references(() => spaces.id, { onDelete: 'cascade' }),
  email: text('email').notNull(),
  user_id: text('user_id').references(() => users.id, { onDelete: 'cascade' }), // Nullable - set when user joins
  joined_at: timestamp('joined_at'), // Nullable - set when user first visits the space
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = ${table.space_id})
      OR auth.user_id() = ${table.user_id}
      OR (SELECT auth.email()) = ${table.email}
    )`,
    modify: sql`(
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = ${table.space_id})
    )`,
  }),
]);

// Tags table
export const tags = pgTable('tags', {
  id: text('id').primaryKey(),
  user_id: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }), // Temporary: still exists in DB
  space_id: text('space_id').notNull().references(() => spaces.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  color: text('color').notNull(),
  type: text('type').default('regular').notNull(), // 'regular' or 'picklist'
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = ${table.space_id} AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = ${table.space_id} AND sc.user_id = auth.user_id()
      )
    )`,
    modify: sql`(
      EXISTS (
        SELECT 1 FROM spaces s WHERE s.id = ${table.space_id} AND s.user_id = auth.user_id()
      ) OR EXISTS (
        SELECT 1 FROM space_collaborators sc WHERE sc.space_id = ${table.space_id} AND sc.user_id = auth.user_id()
      )
    )`,
  }),
]);

// Task tags junction table
export const taskTags = pgTable('task_tags', {
  id: text('id').primaryKey(),
  task_id: text('task_id').notNull().references(() => tasks.id, { onDelete: 'cascade' }),
  tag_id: text('tag_id').notNull().references(() => tags.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = ${table.task_id}
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
    modify: sql`(
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = ${table.task_id}
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = ${table.tag_id}
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
  }),
]);

// Picklist values table - stores predefined values for picklist tags
export const picklistValues = pgTable('picklist_values', {
  id: text('id').primaryKey(),
  tag_id: text('tag_id').notNull().references(() => tags.id, { onDelete: 'cascade' }),
  value: text('value').notNull(),
  position: integer('position').notNull().default(0),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = ${table.tag_id}
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
    modify: sql`(
      EXISTS (
        SELECT 1 FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = ${table.tag_id}
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
  }),
]);

// Task picklist selections table - stores which picklist value is selected for each task-tag combination
export const taskPicklistSelections = pgTable('task_picklist_selections', {
  id: text('id').primaryKey(),
  task_id: text('task_id').notNull().references(() => tasks.id, { onDelete: 'cascade' }),
  tag_id: text('tag_id').notNull().references(() => tags.id, { onDelete: 'cascade' }),
  picklist_value_id: text('picklist_value_id').notNull().references(() => picklistValues.id, { onDelete: 'cascade' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => [
  crudPolicy({
    role: authenticatedRole,
    read: sql`(
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = ${table.task_id}
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
    modify: sql`(
      EXISTS (
        SELECT 1
        FROM tasks t
        JOIN lists l ON l.id = t.list_id
        WHERE t.id = ${table.task_id}
          AND (
            l.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = l.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
      AND EXISTS (
        SELECT 1
        FROM tags tg
        JOIN spaces s ON s.id = tg.space_id
        WHERE tg.id = ${table.tag_id}
          AND (
            s.user_id = auth.user_id()
            OR EXISTS (
              SELECT 1 FROM space_collaborators sc
              WHERE sc.space_id = tg.space_id AND sc.user_id = auth.user_id()
            )
          )
      )
    )`,
  }),
]);

// Define relations
export const usersRelations = relations(users, ({ many }) => ({
  settings: many(userSettings),
  spaces: many(spaces),
  lists: many(lists),
  tasks: many(tasks),
  taskActivities: many(taskActivities),
  tags: many(tags),
  spaceCollaborations: many(spaceCollaborators),
}));

export const userSettingsRelations = relations(userSettings, ({ one }) => ({
  user: one(users, {
    fields: [userSettings.user_id],
    references: [users.id],
  }),
}));

export const spacesRelations = relations(spaces, ({ one, many }) => ({
  user: one(users, {
    fields: [spaces.user_id],
    references: [users.id],
  }),
  lists: many(lists),
  collaborators: many(spaceCollaborators),
}));

export const spaceCollaboratorsRelations = relations(spaceCollaborators, ({ one }) => ({
  space: one(spaces, {
    fields: [spaceCollaborators.space_id],
    references: [spaces.id],
  }),
  user: one(users, {
    fields: [spaceCollaborators.user_id],
    references: [users.id],
  }),
}));

export const listsRelations = relations(lists, ({ one, many }) => ({
  user: one(users, {
    fields: [lists.user_id],
    references: [users.id],
  }),
  space: one(spaces, {
    fields: [lists.space_id],
    references: [spaces.id],
  }),
  tasks: many(tasks),
}));

export const tasksRelations = relations(tasks, ({ one, many }) => ({
  user: one(users, {
    fields: [tasks.user_id],
    references: [users.id],
  }),
  list: one(lists, {
    fields: [tasks.list_id],
    references: [lists.id],
  }),
  parentTask: one(tasks, {
    fields: [tasks.parent_task_id],
    references: [tasks.id],
    relationName: 'parentChild',
  }),
  subtasks: many(tasks, {
    relationName: 'parentChild',
  }),
  activities: many(taskActivities),
  taskTags: many(taskTags),
  taskPicklistSelections: many(taskPicklistSelections),
}));

export const taskActivitiesRelations = relations(taskActivities, ({ one }) => ({
  user: one(users, {
    fields: [taskActivities.user_id],
    references: [users.id],
  }),
  task: one(tasks, {
    fields: [taskActivities.task_id],
    references: [tasks.id],
  }),
}));

export const tagsRelations = relations(tags, ({ one, many }) => ({
  space: one(spaces, {
    fields: [tags.space_id],
    references: [spaces.id],
  }),
  taskTags: many(taskTags),
  picklistValues: many(picklistValues),
  taskPicklistSelections: many(taskPicklistSelections),
}));

export const taskTagsRelations = relations(taskTags, ({ one }) => ({
  task: one(tasks, {
    fields: [taskTags.task_id],
    references: [tasks.id],
  }),
  tag: one(tags, {
    fields: [taskTags.tag_id],
    references: [tags.id],
  }),
}));

export const picklistValuesRelations = relations(picklistValues, ({ one, many }) => ({
  tag: one(tags, {
    fields: [picklistValues.tag_id],
    references: [tags.id],
  }),
  taskPicklistSelections: many(taskPicklistSelections),
}));

export const taskPicklistSelectionsRelations = relations(taskPicklistSelections, ({ one }) => ({
  task: one(tasks, {
    fields: [taskPicklistSelections.task_id],
    references: [tasks.id],
  }),
  tag: one(tags, {
    fields: [taskPicklistSelections.tag_id],
    references: [tags.id],
  }),
  picklistValue: one(picklistValues, {
    fields: [taskPicklistSelections.picklist_value_id],
    references: [picklistValues.id],
  }),
}));

// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type UserSettings = typeof userSettings.$inferSelect;
export type NewUserSettings = typeof userSettings.$inferInsert;

export type Space = typeof spaces.$inferSelect;
export type NewSpace = typeof spaces.$inferInsert;

export type SpaceCollaborator = typeof spaceCollaborators.$inferSelect;
export type NewSpaceCollaborator = typeof spaceCollaborators.$inferInsert;

export type List = typeof lists.$inferSelect;
export type NewList = typeof lists.$inferInsert;

export type Task = typeof tasks.$inferSelect;
export type NewTask = typeof tasks.$inferInsert;

export type TaskActivity = typeof taskActivities.$inferSelect;
export type NewTaskActivity = typeof taskActivities.$inferInsert;

export type Tag = typeof tags.$inferSelect;
export type NewTag = typeof tags.$inferInsert;

export type TaskTag = typeof taskTags.$inferSelect;
export type NewTaskTag = typeof taskTags.$inferInsert;

export type PicklistValue = typeof picklistValues.$inferSelect;
export type NewPicklistValue = typeof picklistValues.$inferInsert;

export type TaskPicklistSelection = typeof taskPicklistSelections.$inferSelect;
export type NewTaskPicklistSelection = typeof taskPicklistSelections.$inferInsert;
