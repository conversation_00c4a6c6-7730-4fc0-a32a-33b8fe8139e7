import { TaskActivity, Task, List } from './db';

// Extended type to include task_title from the join query
export interface ActivityWithTaskTitle extends TaskActivity {
  task_title: string;
}

// Extended type to include list information from the join query
export interface TaskWithList extends Task {
  list_color: string | null;
}

// Extended type to include subtasks
export interface TaskWithSubtasks extends Task {
  subtasks?: Task[];
}



// Tag Filter Types
export interface TagFilter {
  id: string;
  name: string;
  color: string;
  type?: 'regular' | 'picklist'; // Optional for backward compatibility
}

export interface TagFilterState {
  activeTag: TagFilter | null;
  isActive: boolean;
}

// Re-export picklist types for convenience
export type {
  TagType,
  ExtendedTag,
  PicklistTag,
  RegularTag,
  AnyTag,
  ExtendedPicklistValue,
  ExtendedTaskPicklistSelection,
  CreatePicklistTagData,
  CreateRegularTagData,
  CreateTagData,
  UpdatePicklistValuesData,
  SelectPicklistValueData,
  PicklistTagWithSelection,
} from './types/picklist';

export {
  isPicklistTag,
  isRegularTag,
  toExtendedTag,
  toPicklistTag,
  toRegularTag,
} from './types/picklist';
