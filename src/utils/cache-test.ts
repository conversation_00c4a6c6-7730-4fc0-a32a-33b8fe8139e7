/**
 * Utility functions to test and verify caching improvements
 * These can be used in the browser console or for debugging
 */

import { queryClient } from '@/lib/query-client';
import { hasCachedData, clearPersistedCache, getCacheInfo } from '@/lib/cache-persistence';

export const CacheTestUtils = {
  /**
   * Check if cache persistence is working
   */
  checkCachePersistence: () => {
    const hasCache = hasCachedData();
    const cacheInfo = getCacheInfo();

    console.log('📦 Cache persistence status:', hasCache ? '✅ Active' : '❌ No cache');
    if (cacheInfo) {
      console.log('📊 Cache info:', cacheInfo);
    }

    return { hasCache, cacheInfo };
  },

  /**
   * Get cache statistics
   */
  getCacheStats: () => {
    const queryCache = queryClient.getQueryCache();
    const allQueries = queryCache.getAll();
    
    const stats = {
      totalQueries: allQueries.length,
      staleQueries: allQueries.filter(q => q.isStale()).length,
      freshQueries: allQueries.filter(q => !q.isStale()).length,
      queryTypes: {} as Record<string, number>
    };

    // Count queries by type
    allQueries.forEach(query => {
      const queryType = query.queryKey[0] as string;
      stats.queryTypes[queryType] = (stats.queryTypes[queryType] || 0) + 1;
    });

    console.log('📊 Cache Statistics:', stats);
    return stats;
  },

  /**
   * Test bulk tag loading vs individual loading
   */
  testBulkTagLoading: (listId: string, userId: string) => {
    const bulkQuery = queryClient.getQueryData(['bulkTaskTags', listId]);
    const individualQueries = queryClient.getQueryCache().getAll()
      .filter(q => q.queryKey[0] === 'taskTags');

    console.log('🏷️ Tag Loading Test:');
    console.log('  Bulk query data:', bulkQuery ? '✅ Available' : '❌ Not loaded');
    console.log('  Individual tag queries:', individualQueries.length);

    if (bulkQuery) {
      const taskCount = Object.keys(bulkQuery as Record<string, any>).length;
      console.log('  Tasks with bulk tags:', taskCount);
      console.log('  🎯 Result: Using bulk loading - no pop-in!');

      // Additional analysis
      const totalTags = Object.values(bulkQuery as Record<string, any[]>)
        .reduce((sum, tags) => sum + tags.length, 0);
      console.log('  Total tags loaded:', totalTags);
      console.log('  Efficiency: 1 request vs', taskCount, 'individual requests');
      console.log('  Savings:', Math.round((1 - 1/taskCount) * 100) + '% fewer requests');
    } else {
      console.log('  ⚠️ Result: Using individual queries - potential pop-in');
      if (individualQueries.length > 0) {
        console.log('  ⚠️ Warning: Individual queries detected - bulk loading not working');
      }
    }

    return {
      bulkQuery: !!bulkQuery,
      individualQueries: individualQueries.length,
      efficiency: bulkQuery ? Object.keys(bulkQuery as Record<string, any>).length : 0
    };
  },

  /**
   * Monitor network requests for tag loading
   */
  monitorTagRequests: () => {
    console.log('🔍 Monitoring tag requests...');
    console.log('Open DevTools → Network tab to see requests');
    console.log('Look for:');
    console.log('  ✅ fetchBulkTaskTags (good - bulk loading)');
    console.log('  ❌ fetchTaskTags (bad - individual loading)');

    // Hook into fetch to monitor requests
    const originalFetch = window.fetch;
    let tagRequestCount = 0;
    let bulkRequestCount = 0;

    window.fetch = async (...args) => {
      const url = args[0]?.toString() || '';

      if (url.includes('fetchTaskTags')) {
        tagRequestCount++;
        console.log('❌ Individual tag request detected:', tagRequestCount);
      } else if (url.includes('fetchBulkTaskTags')) {
        bulkRequestCount++;
        console.log('✅ Bulk tag request detected:', bulkRequestCount);
      }

      return originalFetch(...args);
    };

    // Restore after 30 seconds
    setTimeout(() => {
      window.fetch = originalFetch;
      console.log('🔍 Monitoring complete:');
      console.log('  Individual requests:', tagRequestCount);
      console.log('  Bulk requests:', bulkRequestCount);
      console.log('  Efficiency:', bulkRequestCount > 0 ? '✅ Using bulk loading' : '❌ Not using bulk loading');
    }, 30000);

    return { tagRequestCount, bulkRequestCount };
  },

  /**
   * Test prefetching effectiveness
   */
  testPrefetching: () => {
    const queryCache = queryClient.getQueryCache();
    const taskQueries = queryCache.getAll().filter(q => q.queryKey[0] === 'tasks');
    const tagQueries = queryCache.getAll().filter(q => q.queryKey[0] === 'bulkTaskTags');

    console.log('🚀 Prefetching Test:');
    console.log('  Prefetched task lists:', taskQueries.length);
    console.log('  Prefetched tag data:', tagQueries.length);
    
    const prefetchedLists = taskQueries.map(q => q.queryKey[1]);
    console.log('  Prefetched list IDs:', prefetchedLists);

    return {
      taskQueries: taskQueries.length,
      tagQueries: tagQueries.length,
      prefetchedLists
    };
  },

  /**
   * Test stale time configuration
   */
  testStaleTimeConfig: () => {
    const queryCache = queryClient.getQueryCache();
    const allQueries = queryCache.getAll();

    const staleTimeTest = allQueries.map(query => {
      const queryType = query.queryKey[0] as string;
      const isStale = query.isStale();
      const lastUpdated = query.state.dataUpdatedAt;
      const timeSinceUpdate = Date.now() - lastUpdated;

      return {
        queryType,
        isStale,
        timeSinceUpdate: Math.round(timeSinceUpdate / 1000), // seconds
        hasData: !!query.state.data
      };
    });

    console.log('⏰ Stale Time Configuration Test:');
    console.table(staleTimeTest);

    return staleTimeTest;
  },

  /**
   * Simulate cache warming
   */
  simulateCacheWarming: async () => {
    console.log('🔥 Simulating cache warming...');
    
    const startTime = Date.now();
    const initialStats = CacheTestUtils.getCacheStats();
    
    // Wait a bit to see if cache warming happens
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const endTime = Date.now();
    const finalStats = CacheTestUtils.getCacheStats();
    
    console.log('🔥 Cache Warming Results:');
    console.log('  Time elapsed:', endTime - startTime, 'ms');
    console.log('  Queries before:', initialStats.totalQueries);
    console.log('  Queries after:', finalStats.totalQueries);
    console.log('  New queries added:', finalStats.totalQueries - initialStats.totalQueries);

    return {
      timeElapsed: endTime - startTime,
      queriesAdded: finalStats.totalQueries - initialStats.totalQueries
    };
  },

  /**
   * Clear all cache for testing
   */
  clearAllCache: () => {
    console.log('🧹 Clearing all cache...');
    queryClient.clear();
    clearPersistedCache();
    console.log('✅ Cache cleared');
  },

  /**
   * Run comprehensive cache test
   */
  runFullCacheTest: async (listId?: string, userId?: string) => {
    console.log('🧪 Running comprehensive cache test...');
    console.log('=====================================');

    const results = {
      persistence: CacheTestUtils.checkCachePersistence(),
      stats: CacheTestUtils.getCacheStats(),
      staleTime: CacheTestUtils.testStaleTimeConfig(),
      prefetching: CacheTestUtils.testPrefetching(),
      bulkTags: listId && userId ? CacheTestUtils.testBulkTagLoading(listId, userId) : null
    };

    console.log('🎯 Test Summary:');
    console.log('  Cache Persistence:', results.persistence ? '✅' : '❌');
    console.log('  Total Queries:', results.stats.totalQueries);
    console.log('  Fresh Queries:', results.stats.freshQueries);
    console.log('  Prefetched Lists:', results.prefetching.taskQueries);
    
    if (results.bulkTags) {
      console.log('  Bulk Tag Loading:', results.bulkTags.bulkQuery ? '✅' : '❌');
    }

    console.log('=====================================');
    return results;
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).CacheTestUtils = CacheTestUtils;
  console.log('🧪 CacheTestUtils available globally. Try: CacheTestUtils.runFullCacheTest()');
}
