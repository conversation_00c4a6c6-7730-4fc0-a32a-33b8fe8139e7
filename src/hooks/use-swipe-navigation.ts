"use client";

import { useRef, useCallback, useEffect } from "react";
import { useMediaQuery } from "./use-media-query";

interface SwipeNavigationOptions {
  onSwipeLeft: () => void;
  onSwipeRight: () => void;
  threshold?: number;
  preventDefaultTouchMove?: boolean;
}

interface TouchData {
  startX: number;
  startY: number;
  startTime: number;
  currentX: number;
  currentY: number;
}

export function useSwipeNavigation({
  onSwipeLeft,
  onSwipeRight,
  threshold = 50,
  preventDefaultTouchMove = false,
}: SwipeNavigationOptions) {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const touchDataRef = useRef<TouchData | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!isMobile) return;

    const touch = e.touches[0];
    touchDataRef.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      currentX: touch.clientX,
      currentY: touch.clientY,
    };
  }, [isMobile]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isMobile || !touchDataRef.current) return;

    const touch = e.touches[0];
    touchDataRef.current.currentX = touch.clientX;
    touchDataRef.current.currentY = touch.clientY;

    // Calculate deltas
    const deltaX = Math.abs(touch.clientX - touchDataRef.current.startX);
    const deltaY = Math.abs(touch.clientY - touchDataRef.current.startY);

    // If horizontal movement is dominant and exceeds threshold, prevent default
    if (preventDefaultTouchMove && deltaX > deltaY && deltaX > threshold / 2) {
      e.preventDefault();
    }
  }, [isMobile, threshold, preventDefaultTouchMove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!isMobile || !touchDataRef.current) return;

    const touchData = touchDataRef.current;
    const deltaX = touchData.currentX - touchData.startX;
    const deltaY = touchData.currentY - touchData.startY;
    const deltaTime = Date.now() - touchData.startTime;

    // Reset touch data
    touchDataRef.current = null;

    // Swipe detection criteria:
    // 1. Horizontal movement must be greater than vertical
    // 2. Horizontal movement must exceed threshold
    // 3. Swipe must be completed within reasonable time (1 second)
    // 4. Minimum velocity check for intentional swipes
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);
    const velocity = absDeltaX / deltaTime; // pixels per millisecond

    if (
      absDeltaX > absDeltaY && // More horizontal than vertical
      absDeltaX > threshold && // Exceeds minimum distance
      deltaTime < 1000 && // Completed within 1 second
      velocity > 0.1 // Minimum velocity (0.1 px/ms = 100 px/s)
    ) {
      if (deltaX > 0) {
        // Swiped right (show previous list)
        onSwipeRight();
      } else {
        // Swiped left (show next list)
        onSwipeLeft();
      }
    }
  }, [isMobile, threshold, onSwipeLeft, onSwipeRight]);

  // Attach event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !isMobile) return;

    // Use passive listeners for better performance, except touchmove when preventing default
    const touchMoveOptions = preventDefaultTouchMove ? { passive: false } : { passive: true };

    container.addEventListener("touchstart", handleTouchStart, { passive: true });
    container.addEventListener("touchmove", handleTouchMove, touchMoveOptions);
    container.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isMobile, handleTouchStart, handleTouchMove, handleTouchEnd, preventDefaultTouchMove]);

  return {
    containerRef,
    isMobile,
  };
}
