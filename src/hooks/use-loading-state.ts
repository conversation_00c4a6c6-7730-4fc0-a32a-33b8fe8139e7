"use client";

import { useState, useEffect } from "react";

interface LoadingStateOptions {
  minLoadingTime?: number; // Minimum time to show loading state (prevents flashing)
  delayMs?: number; // Delay before showing loading state
}

export function useLoadingState(
  isLoading: boolean, 
  options: LoadingStateOptions = {}
) {
  const { minLoadingTime = 300, delayMs = 150 } = options;
  const [showLoading, setShowLoading] = useState(false);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  useEffect(() => {
    let delayTimer: NodeJS.Timeout;
    let minTimeTimer: NodeJS.Timeout;

    if (isLoading) {
      // Start delay timer
      delayTimer = setTimeout(() => {
        setShowLoading(true);
        setLoadingStartTime(Date.now());
      }, delayMs);
    } else {
      // Clear delay timer if loading stops before delay
      if (delayTimer) {
        clearTimeout(delayTimer);
      }

      // If we were showing loading, ensure minimum time
      if (showLoading && loadingStartTime) {
        const elapsed = Date.now() - loadingStartTime;
        const remaining = Math.max(0, minLoadingTime - elapsed);

        if (remaining > 0) {
          minTimeTimer = setTimeout(() => {
            setShowLoading(false);
            setLoadingStartTime(null);
          }, remaining);
        } else {
          setShowLoading(false);
          setLoadingStartTime(null);
        }
      } else {
        setShowLoading(false);
        setLoadingStartTime(null);
      }
    }

    return () => {
      if (delayTimer) clearTimeout(delayTimer);
      if (minTimeTimer) clearTimeout(minTimeTimer);
    };
  }, [isLoading, delayMs, minLoadingTime, showLoading, loadingStartTime]);

  return showLoading;
}

interface MultipleLoadingStateOptions extends LoadingStateOptions {
  strategy?: "any" | "all"; // Show loading if any query is loading or all queries are loading
}

export function useMultipleLoadingState(
  loadingStates: boolean[],
  options: MultipleLoadingStateOptions = {}
) {
  const { strategy = "any", ...loadingOptions } = options;
  
  const isLoading = strategy === "any" 
    ? loadingStates.some(state => state)
    : loadingStates.every(state => state);

  return useLoadingState(isLoading, loadingOptions);
}

// Hook for coordinating navigation loading with page content
export function useNavigationLoadingState(pageIsLoading: boolean) {
  const [isNavigating, setIsNavigating] = useState(false);
  
  useEffect(() => {
    // Show navigation loading immediately when page starts loading
    if (pageIsLoading) {
      setIsNavigating(true);
    } else {
      // Delay hiding navigation loading to coordinate with page content
      const timer = setTimeout(() => {
        setIsNavigating(false);
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [pageIsLoading]);

  return isNavigating;
}

// Hook for managing skeleton vs spinner loading states
export function useLoadingVariant(
  isLoading: boolean,
  hasData: boolean,
  options: { preferSkeleton?: boolean } = {}
) {
  const { preferSkeleton = true } = options;
  
  // Show skeleton on initial load when we have no data
  // Show spinner on subsequent loads when we have cached data
  const shouldShowSkeleton = isLoading && (!hasData || preferSkeleton);
  const shouldShowSpinner = isLoading && hasData && !preferSkeleton;

  return {
    showSkeleton: shouldShowSkeleton,
    showSpinner: shouldShowSpinner,
    showContent: !isLoading
  };
}
