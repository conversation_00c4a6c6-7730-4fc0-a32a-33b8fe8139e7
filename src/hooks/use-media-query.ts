"use client";

import { useState, useEffect } from "react";

export function useMediaQuery(query: string): boolean {
  // Initialize with a function to avoid hydration mismatch
  const [matches, setMatches] = useState(() => {
    // During SSR, return false (mobile-first approach)
    if (typeof window === 'undefined') return false;

    // During hydration, get the actual media query result immediately
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    const media = window.matchMedia(query);

    // Set initial value (in case it changed between hydration and effect)
    setMatches(media.matches);

    // Create event listener
    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add listener
    media.addEventListener("change", listener);

    // Cleanup
    return () => media.removeEventListener("change", listener);
  }, [query]);

  return matches;
}
