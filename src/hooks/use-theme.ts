"use client";

import { useTheme as useNextTheme } from "next-themes";
import { useUser } from "@stackframe/stack";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useUserSettingsQuery, useUpdateSettingsMutation } from "@/lib/queries";

type ThemeOption = "light" | "dark" | "system";

export function useTheme() {
  const { theme: nextTheme, setTheme: setNextTheme, systemTheme, resolvedTheme } = useNextTheme();
  const user = useUser();
  const queryClient = useQueryClient();

  // Query to get user settings from database using the centralized query
  const { data: userSettings, isLoading } = useUserSettingsQuery(user?.id || "");

  // Mutation to update theme in database using the centralized mutation
  const updateSettingsMutation = useUpdateSettingsMutation(user?.id || "");

  // Sync database theme with next-themes on initial load
  useEffect(() => {
    if (userSettings?.theme && userSettings.theme !== nextTheme && !isLoading) {
      setNextTheme(userSettings.theme);
    }
  }, [userSettings?.theme, nextTheme, setNextTheme, isLoading]);

  // Function to set theme (updates both next-themes and database)
  const setTheme = async (theme: ThemeOption) => {
    // Immediately update next-themes for instant UI feedback
    setNextTheme(theme);

    // Update database in background
    if (user?.id) {
      try {
        await updateSettingsMutation.mutateAsync({ theme });
      } catch (error) {
        console.error("Failed to save theme to database:", error);
        // Optionally revert the theme if database update fails
        // setNextTheme(userSettings?.theme || "system");
      }
    }
  };

  return {
    theme: (userSettings?.theme as ThemeOption) || "system",
    setTheme,
    systemTheme,
    resolvedTheme,
    isLoading: isLoading || updateSettingsMutation.isPending,
  };
}
