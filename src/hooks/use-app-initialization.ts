import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useUser } from '@stackframe/stack';
import { queryKeys } from '@/lib/queries';
import { fetchLists, fetchTaskCounts } from '@/app/actions/lists';
import { fetchTags } from '@/app/actions/tags';
import { fetchUserSettings } from '@/app/actions/settings';
import { fetchSpaces } from '@/app/actions/spaces';
import { hasCachedData } from '@/lib/cache-persistence';

/**
 * Hook to initialize app with critical data preloading
 * This ensures instant data availability and eliminates loading states
 */
export function useAppInitialization() {
  const user = useUser();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user?.id) return;

    const initializeApp = async () => {
      const hasCache = hasCachedData();
      
      // If we have cached data, prefetch in background with lower priority
      // If no cache, prefetch immediately for faster initial load
      const prefetchOptions = {
        staleTime: hasCache ? 0 : 15 * 60 * 1000, // Force refetch if cached, otherwise cache for 15 min
      };

      try {
        // Prefetch critical data in parallel
        const prefetchPromises = [
          // User's spaces - critical for navigation header
          queryClient.prefetchQuery({
            queryKey: queryKeys.spaces(user.id),
            queryFn: () => fetchSpaces(user.id),
            ...prefetchOptions,
          }),

          // User's lists - critical for navigation
          queryClient.prefetchQuery({
            queryKey: queryKeys.lists(user.id),
            queryFn: () => fetchLists(user.id),
            ...prefetchOptions,
          }),

          // User's tags - needed for task display
          queryClient.prefetchQuery({
            queryKey: queryKeys.tags(user.id),
            queryFn: () => fetchTags(user.id),
            ...prefetchOptions,
          }),

          // Task counts - needed for navigation badges
          queryClient.prefetchQuery({
            queryKey: queryKeys.taskCounts(user.id),
            queryFn: () => fetchTaskCounts(user.id),
            ...prefetchOptions,
          }),

          // User settings - needed for theme and preferences
          queryClient.prefetchQuery({
            queryKey: queryKeys.userSettings(user.id),
            queryFn: () => fetchUserSettings(user.id),
            ...prefetchOptions,
          }),
        ];

        // Wait for critical data to load
        await Promise.allSettled(prefetchPromises);

        // After critical data is loaded, prefetch the first list's tasks
        const lists = queryClient.getQueryData(queryKeys.lists(user.id)) as any[];
        if (lists && lists.length > 0) {
          const firstList = lists[0];
          
          // Import fetchTasksByList dynamically to avoid circular dependencies
          const { fetchTasksByList } = await import('@/app/actions/tasks');
          
          // Prefetch first list's tasks
          await queryClient.prefetchQuery({
            queryKey: queryKeys.tasks(firstList.id, 'position'),
            queryFn: () => fetchTasksByList(firstList.id, 'position'),
            staleTime: 15 * 60 * 1000,
          });

          // Prefetch bulk task tags for first list - using TRUE bulk loading
          queryClient.prefetchQuery({
            queryKey: queryKeys.bulkTaskTags(firstList.id),
            queryFn: async () => {
              const tasks = await fetchTasksByList(firstList.id, 'position');
              if (tasks.length === 0) return {};

              const taskIds = tasks.map(task => task.id);
              const { fetchBulkTaskTags } = await import('@/app/actions/tags');
              return await fetchBulkTaskTags(taskIds, user.id);
            },
            staleTime: 15 * 60 * 1000,
          });
        }

      } catch (error) {
        console.warn('App initialization prefetch failed:', error);
        // Don't throw - app should still work without prefetched data
      }
    };

    // Delay initialization slightly to not block initial render
    const timer = setTimeout(initializeApp, 100);
    
    return () => clearTimeout(timer);
  }, [user?.id, queryClient]);

  return {
    // Return utility functions if needed
    hasCachedData: hasCachedData(),
  };
}

/**
 * Hook to warm up cache for better navigation performance
 * Should be called after initial app load is complete
 */
export function useCacheWarming() {
  const user = useUser();
  const queryClient = useQueryClient();

  const warmCache = async () => {
    if (!user?.id) return;

    try {
      // Get all lists
      const lists = queryClient.getQueryData(queryKeys.lists(user.id)) as any[];
      if (!lists || lists.length <= 1) return;

      // Import actions dynamically
      const { fetchTasksByList } = await import('@/app/actions/tasks');

      // Warm cache for all lists (except the first one which should already be loaded)
      const warmingPromises = lists.slice(1).map(async (list) => {
        // Prefetch tasks
        await queryClient.prefetchQuery({
          queryKey: queryKeys.tasks(list.id, 'position'),
          queryFn: () => fetchTasksByList(list.id, 'position'),
          staleTime: 15 * 60 * 1000,
        });

        // Prefetch bulk task tags - using TRUE bulk loading
        return queryClient.prefetchQuery({
          queryKey: queryKeys.bulkTaskTags(list.id),
          queryFn: async () => {
            const tasks = await fetchTasksByList(list.id, 'position');
            if (tasks.length === 0) return {};

            const taskIds = tasks.map(task => task.id);
            const { fetchBulkTaskTags } = await import('@/app/actions/tags');
            return await fetchBulkTaskTags(taskIds, user.id);
          },
          staleTime: 15 * 60 * 1000,
        });
      });

      // Execute warming in batches to avoid overwhelming the server
      const batchSize = 2;
      for (let i = 0; i < warmingPromises.length; i += batchSize) {
        const batch = warmingPromises.slice(i, i + batchSize);
        await Promise.allSettled(batch);
        
        // Small delay between batches
        if (i + batchSize < warmingPromises.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

    } catch (error) {
      console.warn('Cache warming failed:', error);
    }
  };

  return { warmCache };
}
