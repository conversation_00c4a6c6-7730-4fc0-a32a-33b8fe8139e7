"use client";

import { useRef, useEffect } from "react";
import { useMediaQuery } from "./use-media-query";

interface ModalSwipeNavigationOptions {
  onSwipeLeft: () => void;
  onSwipeRight: () => void;
  threshold?: number;
  enabled?: boolean;
}

interface TouchData {
  startX: number;
  startY: number;
  startTime: number;
  currentX: number;
  currentY: number;
}

export function useModalSwipeNavigation({
  onSwipeLeft,
  onSwipeRight,
  threshold = 80,
  enabled = true,
}: ModalSwipeNavigationOptions) {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const touchDataRef = useRef<TouchData | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Stable references to callbacks to prevent unnecessary re-renders
  const onSwipeLeftRef = useRef(onSwipeLeft);
  const onSwipeRightRef = useRef(onSwipeRight);
  onSwipeLeftRef.current = onSwipeLeft;
  onSwipeRightRef.current = onSwipeRight;

  // Attach event listeners - use document-level listeners for better reliability
  useEffect(() => {
    if (!isMobile || !enabled) {
      return;
    }
    
    // Create stable event handlers that check if the touch is on our modal
    const onTouchStart = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled) return;

      // Check if the touch is within our modal
      const target = e.target as Element;
      if (!container.contains(target)) return;

      // Don't interfere with interactive elements
      const isInteractiveElement = target.closest('button, input, textarea, select, a, [role="button"], [tabindex]');
      if (isInteractiveElement) return;

      const touch = e.touches[0];
      touchDataRef.current = {
        startX: touch.clientX,
        startY: touch.clientY,
        startTime: Date.now(),
        currentX: touch.clientX,
        currentY: touch.clientY,
      };
    };

    const onTouchMove = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled || !touchDataRef.current) return;

      const touch = e.touches[0];
      touchDataRef.current.currentX = touch.clientX;
      touchDataRef.current.currentY = touch.clientY;

      // Calculate deltas
      const deltaX = Math.abs(touch.clientX - touchDataRef.current.startX);
      const deltaY = Math.abs(touch.clientY - touchDataRef.current.startY);

      // If horizontal movement is dominant and exceeds threshold, prevent default
      if (deltaX > deltaY && deltaX > threshold / 2) {

        e.preventDefault();
        e.stopPropagation();
      }
    };

    const onTouchEnd = (e: TouchEvent) => {
      const container = containerRef.current;
      if (!container || !isMobile || !enabled || !touchDataRef.current) return;

      const touchData = touchDataRef.current;
      const deltaX = touchData.currentX - touchData.startX;
      const deltaY = touchData.currentY - touchData.startY;
      const deltaTime = Date.now() - touchData.startTime;


      touchDataRef.current = null;

      // Swipe detection criteria:
      // 1. Horizontal movement must be greater than vertical
      // 2. Horizontal movement must exceed threshold
      // 3. Swipe must be completed within reasonable time (1 second)
      // 4. Minimum velocity check for intentional swipes
      const absDeltaX = Math.abs(deltaX);
      const absDeltaY = Math.abs(deltaY);
      const velocity = absDeltaX / deltaTime; // pixels per millisecond

      if (
        absDeltaX > absDeltaY && // More horizontal than vertical
        absDeltaX > threshold && // Exceeds minimum distance
        deltaTime < 1000 && // Completed within 1 second
        velocity > 0.1 // Minimum velocity (0.1 px/ms = 100 px/s)
      ) {

        if (deltaX > 0) {
          // Swiped right (show previous task)
          onSwipeRightRef.current();
        } else {
          // Swiped left (show next task)
          onSwipeLeftRef.current();
        }
      }
    };

    // Attach to document to ensure we always get events
    // Use capture phase with higher priority than swipe-to-dismiss
    document.addEventListener("touchstart", onTouchStart, { passive: false, capture: true });
    document.addEventListener("touchmove", onTouchMove, { passive: false, capture: true });
    document.addEventListener("touchend", onTouchEnd, { passive: false, capture: true });

    return () => {
      document.removeEventListener("touchstart", onTouchStart, true);
      document.removeEventListener("touchmove", onTouchMove, true);
      document.removeEventListener("touchend", onTouchEnd, true);
    };
  }, [isMobile, enabled, threshold]);

  return {
    containerRef,
    isMobile,
  };
}
