"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Tag } from '@/lib/db';

interface UseSmartTagSearchOptions {
  availableTags: Tag[];
  selectedTags?: Tag[];
  onSearchTags: (searchTerm: string) => Promise<Tag[]>;
  clientSearchThreshold?: number; // Character count to switch from client to server search
  debounceMs?: number;
}

interface SmartTagSearchResult {
  filteredTags: Tag[];
  isSearching: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

/**
 * Smart tag search hook that provides:
 * - Instant client-side filtering for short search terms
 * - Debounced server search for longer terms
 * - Minimal loading states to prevent UI flashing
 * - Automatic filtering of selected tags
 */
export function useSmartTagSearch({
  availableTags,
  selectedTags = [],
  onSearchTags,
  clientSearchThreshold = 2,
  debounceMs = 300,
}: UseSmartTagSearchOptions): SmartTagSearchResult {
  const [searchTerm, setSearchTerm] = useState('');
  const [serverResults, setServerResults] = useState<Tag[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Use refs to store latest values without causing re-renders
  const onSearchTagsRef = useRef(onSearchTags);
  const selectedTagsRef = useRef(selectedTags);

  // Update refs when values change
  useEffect(() => {
    onSearchTagsRef.current = onSearchTags;
  }, [onSearchTags]);

  useEffect(() => {
    selectedTagsRef.current = selectedTags;
  }, [selectedTags]);

  // Memoize available tags to prevent unnecessary recalculations
  const memoizedAvailableTags = useMemo(() => availableTags, [availableTags]);

  // Client-side filtered tags for instant results
  const clientFilteredTags = useMemo(() => {
    if (!searchTerm.trim()) {
      return memoizedAvailableTags.filter(
        tag => !selectedTagsRef.current.some(selected => selected.id === tag.id)
      );
    }

    return memoizedAvailableTags.filter(tag => {
      const matchesSearch = tag.name.toLowerCase().includes(searchTerm.toLowerCase());
      const notSelected = !selectedTagsRef.current.some(selected => selected.id === tag.id);
      return matchesSearch && notSelected;
    });
  }, [memoizedAvailableTags, searchTerm]);

  // Server search effect for longer terms
  useEffect(() => {
    if (!searchTerm.trim() || searchTerm.length <= clientSearchThreshold) {
      setServerResults([]);
      setIsSearching(false);
      return;
    }

    const searchServer = async () => {
      setIsSearching(true);
      try {
        const results = await onSearchTagsRef.current(searchTerm);
        const unselectedResults = results.filter(
          tag => !selectedTagsRef.current.some(selected => selected.id === tag.id)
        );
        setServerResults(unselectedResults);
      } catch (error) {
        console.error('Error searching tags:', error);
        setServerResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(searchServer, debounceMs);
    return () => clearTimeout(timeoutId);
  }, [searchTerm, clientSearchThreshold, debounceMs]);

  // Determine which results to show
  const filteredTags = useMemo(() => {
    if (!searchTerm.trim() || searchTerm.length <= clientSearchThreshold) {
      return clientFilteredTags;
    }
    return serverResults;
  }, [searchTerm, clientSearchThreshold, clientFilteredTags, serverResults]);

  return {
    filteredTags,
    isSearching,
    searchTerm,
    setSearchTerm,
  };
}
