"use client";

import { useRef, useCallback } from "react";
import { useMediaQuery } from "./use-media-query";

interface LongPressOptions {
  onLongPress: () => void;
  onClick?: () => void;
  delay?: number; // Delay in milliseconds before triggering long press
  threshold?: number; // Movement threshold in pixels
}

interface TouchData {
  startX: number;
  startY: number;
  startTime: number;
}

export function useLongPress({
  onLongPress,
  onClick,
  delay = 500, // 500ms default delay
  threshold = 10, // 10px movement threshold
}: LongPressOptions) {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const touchDataRef = useRef<TouchData | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressTriggeredRef = useRef(false);

  // Stable references to callbacks
  const onLongPressRef = useRef(onLongPress);
  const onClickRef = useRef(onClick);
  onLongPressRef.current = onLongPress;
  onClickRef.current = onClick;

  const clearLongPressTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;

    const touch = e.touches[0];
    touchDataRef.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
    };

    isLongPressTriggeredRef.current = false;

    // Set up long press timeout
    timeoutRef.current = setTimeout(() => {
      if (touchDataRef.current) {
        isLongPressTriggeredRef.current = true;
        
        // Provide haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
        
        onLongPressRef.current();
      }
    }, delay);
  }, [isMobile, delay]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !touchDataRef.current) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - touchDataRef.current.startX);
    const deltaY = Math.abs(touch.clientY - touchDataRef.current.startY);
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // If movement exceeds threshold, cancel long press
    if (totalMovement > threshold) {
      clearLongPressTimeout();
      touchDataRef.current = null;
    }
  }, [isMobile, threshold, clearLongPressTimeout]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;

    clearLongPressTimeout();

    // If long press was triggered, don't trigger click
    if (isLongPressTriggeredRef.current) {
      e.preventDefault();
      e.stopPropagation();
      touchDataRef.current = null;
      return;
    }

    // If we have touch data and it was a quick tap, trigger click
    if (touchDataRef.current && onClickRef.current) {
      const touchDuration = Date.now() - touchDataRef.current.startTime;
      
      // Only trigger click for quick taps (less than delay time)
      if (touchDuration < delay) {
        onClickRef.current();
      }
    }

    touchDataRef.current = null;
  }, [isMobile, delay, clearLongPressTimeout]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isMobile) return;

    // For desktop, just handle regular click
    if (onClickRef.current) {
      onClickRef.current();
    }
  }, [isMobile]);

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    clearLongPressTimeout();
    touchDataRef.current = null;
    isLongPressTriggeredRef.current = false;
  }, [clearLongPressTimeout]);

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onMouseDown: handleMouseDown,
    cleanup,
  };
}
