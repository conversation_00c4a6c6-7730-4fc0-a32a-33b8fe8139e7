"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { LayoutDashboard, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";


// Custom N Icon component for Tasks
const NIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="Tasks"
    width={24}
    height={24}
    className={`${className} object-contain`}
    style={style}
  />
);

const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
  },
  {
    href: "/tasks",
    label: "Tasks",
    icon: NIcon,
  },
  {
    href: "/calendar",
    label: "Calendar",
    icon: Calendar,
  },
];

export function BottomNavigation() {
  const [currentPath, setCurrentPath] = useState("");
  const pathname = usePathname();

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Get grey filter for inactive N icon to match other inactive tab icons
  const getInactiveNIconFilter = () => {
    // Convert white N icon to muted grey color similar to other inactive icons
    return "brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(90%) contrast(90%)";
  };

  return (
    <div className="fixed bottom-0 left-0 z-50 w-full h-14 bg-background md:hidden">
      <div className="container-max-width mx-auto">
        <div className="grid h-full grid-cols-3">
          {routes.map((route) => {
            const Icon = route.icon;
            const isActive = currentPath === route.href;
            const isTasksTab = route.href === "/tasks";

            return (
              <Link
                key={route.href}
                href={route.href}
                className="flex items-center justify-center"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-14 w-14 ${
                    isActive ? "text-white" : "text-muted-foreground"
                  }`}
                >
                  {isTasksTab ? (
                    <Icon
                      className="h-6 w-6"
                      style={!isActive ? {
                        filter: getInactiveNIconFilter()
                      } : undefined}
                    />
                  ) : (
                    <Icon className="h-6 w-6" />
                  )}
                  <span className="sr-only">{route.label}</span>
                </Button>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}
