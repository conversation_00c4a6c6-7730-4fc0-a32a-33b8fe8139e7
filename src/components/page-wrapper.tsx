"use client";

import { cn } from "@/lib/utils";
import { Navigation } from "@/components/navigation";
import { PageLoadingBoundary } from "@/components/ui/loading-boundary";

interface PageWrapperProps {
  children: React.ReactNode;
  isLoading?: boolean;
  showNavigation?: boolean;
  navigationProps?: {
    isLoading?: boolean;
    showSkeleton?: boolean;
  };
  className?: string;
  title?: string;
  error?: Error | null;
}

export function PageWrapper({
  children,
  isLoading = false,
  showNavigation = true,
  navigationProps = {},
  className,
  title,
  error
}: PageWrapperProps) {
  return (
    <>
      {showNavigation && (
        <Navigation 
          {...navigationProps}
          isLoading={isLoading || navigationProps.isLoading}
        />
      )}
      
      <main className={cn("min-h-screen", className)}>
        <PageLoadingBoundary 
          isLoading={isLoading}
          title={title}
        >
          {error ? (
            <div className="px-2 md:px-4 py-3">
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="text-destructive text-4xl mb-4">⚠️</div>
                <h2 className="text-xl font-medium mb-2">Something went wrong</h2>
                <p className="text-muted-foreground mb-4">
                  {error.message || "An unexpected error occurred"}
                </p>
                <button 
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  Reload Page
                </button>
              </div>
            </div>
          ) : (
            children
          )}
        </PageLoadingBoundary>
      </main>
    </>
  );
}

interface ProtectedPageWrapperProps extends PageWrapperProps {
  requireAuth?: boolean;
}

export function ProtectedPageWrapper({
  requireAuth = true,
  ...props
}: ProtectedPageWrapperProps) {
  // This wrapper can be extended to include auth checks
  // For now, it's the same as PageWrapper but can be enhanced
  return <PageWrapper {...props} />;
}
