"use client";

import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  variant?: "default" | "gradient" | "muted";
}

const sizeClasses = {
  sm: "h-4 w-4",
  md: "h-6 w-6", 
  lg: "h-8 w-8",
  xl: "h-12 w-12"
};

const variantClasses = {
  default: "text-muted-foreground",
  gradient: "text-transparent bg-clip-text bg-gradient-to-r from-primary via-secondary to-accent",
  muted: "text-muted-foreground/60"
};

export function LoadingSpinner({ 
  size = "md", 
  className,
  variant = "default" 
}: LoadingSpinnerProps) {
  return (
    <Loader2 
      className={cn(
        "animate-spin",
        sizeClasses[size],
        variantClasses[variant],
        className
      )} 
    />
  );
}

interface LoadingContainerProps {
  children?: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "gradient" | "muted";
  className?: string;
  text?: string;
}

export function LoadingContainer({ 
  children, 
  size = "md", 
  variant = "default",
  className,
  text = "Loading..."
}: LoadingContainerProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center gap-3 py-8",
      className
    )}>
      <LoadingSpinner size={size} variant={variant} />
      {text && (
        <p className="text-sm text-muted-foreground font-light">
          {text}
        </p>
      )}
      {children}
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}

export function LoadingOverlay({ 
  isLoading, 
  children, 
  loadingText = "Loading...",
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 transition-opacity duration-300">
          <LoadingContainer text={loadingText} />
        </div>
      )}
    </div>
  );
}
