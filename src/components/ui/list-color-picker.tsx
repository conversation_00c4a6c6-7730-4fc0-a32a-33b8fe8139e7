"use client";

import * as React from "react";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { PICKER_LIST_COLORS, COLORLESS_OPTION, type ListColor, getListColorByValue } from "@/lib/list-colors";

export interface ListColorPickerProps {
  value?: string | null;
  onChange: (color: string | null) => void;
  className?: string;
  disabled?: boolean;
}

export function ListColorPicker({
  value,
  onChange,
  className,
  disabled = false,
}: ListColorPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const selectedColor = getListColorByValue(value);

  const handleColorSelect = (color: ListColor) => {
    onChange(color.value);
    setIsOpen(false);
  };

  const handleClearColor = () => {
    onChange(null);
    setIsOpen(false);
  };

  return (
    <div className="flex items-center gap-1">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "h-8 w-8 p-0 border-2",
              disabled && "opacity-50 cursor-not-allowed",
              className
            )}
            disabled={disabled}
            aria-label="Select list color"
          >
            <div
              className="h-4 w-4 rounded-sm"
              style={{
                backgroundColor: selectedColor?.value || selectedColor?.displayColor || COLORLESS_OPTION.displayColor,
              }}
            />
          </Button>
        </PopoverTrigger>

      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Choose a list color</h4>

          <div className="grid grid-cols-4 gap-2">
            {PICKER_LIST_COLORS.map((color) => (
              <button
                key={color.name}
                type="button"
                className={cn(
                  "relative h-8 w-8 rounded-md border-2 border-transparent transition-all hover:scale-110 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                  value === color.value && "border-foreground ring-2 ring-ring ring-offset-2"
                )}
                style={{
                  backgroundColor: color.value,
                }}
                onClick={() => handleColorSelect(color)}
                aria-label={color.name}
                title={color.name}
              >
                {value === color.value && (
                  <Check
                    className="h-4 w-4 absolute inset-0 m-auto text-white drop-shadow-sm"
                    strokeWidth={3}
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      </PopoverContent>
      </Popover>

      {/* X button next to selector when color is selected */}
      {value && (
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
          onClick={handleClearColor}
          aria-label="Remove color"
          title="Remove color"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
