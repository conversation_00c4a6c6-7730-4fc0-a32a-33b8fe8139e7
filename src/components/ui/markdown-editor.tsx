"use client";

import * as React from "react";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { MarkdownToolbar } from "@/components/ui/markdown-toolbar";
import { cn } from "@/lib/utils";

export interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minRows?: number;
  maxRows?: number;
  id?: string;
}

export const MarkdownEditor = React.forwardRef<HTMLDivElement, MarkdownEditorProps>(
  ({ value, onChange, placeholder, className, id }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [showMarkdown, setShowMarkdown] = React.useState(false);

    // Convert markdown to HTML for Tiptap
    const htmlToMarkdown = (html: string): string => {
      // Simple HTML to markdown conversion
      return html
        .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
        .replace(/<em>(.*?)<\/em>/g, '*$1*')
        .replace(/<ul><li>(.*?)<\/li><\/ul>/g, '- $1')
        .replace(/<ol><li>(.*?)<\/li><\/ol>/g, '1. $1')
        .replace(/<li>(.*?)<\/li>/g, '- $1')
        .replace(/<p>(.*?)<\/p>/g, '$1\n')
        .replace(/<br\s*\/?>/g, '\n')
        .trim();
    };

    // Convert markdown to HTML for Tiptap
    const markdownToHtml = (markdown: string): string => {
      // Simple markdown to HTML conversion
      return markdown
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^- (.+)$/gm, '<ul><li>$1</li></ul>')
        .replace(/^\d+\. (.+)$/gm, '<ol><li>$1</li></ol>')
        .replace(/\n/g, '<br>');
    };

    const handleFormat = React.useCallback((format: string, prefix: string, suffix?: string) => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const selectedText = value.substring(start, end);




      
      let newText: string;
      let newCursorPos: number;

      if (format === "bullet" || format === "numbered") {
        // Handle list formatting
        const lines = value.split('\n');
        const startLine = value.substring(0, start).split('\n').length - 1;
        const endLine = value.substring(0, end).split('\n').length - 1;

        for (let i = startLine; i <= endLine; i++) {
          if (lines[i] !== undefined) {
            // Check if line already has this prefix
            const trimmedPrefix = prefix.trim();
            if (!lines[i].trim().startsWith(trimmedPrefix)) {
              // Add prefix to the beginning of the line
              lines[i] = prefix + lines[i];
            }
          }
        }

        newText = lines.join('\n');
        newCursorPos = end + prefix.length;
      } else {
        // Handle inline formatting (bold, italic)
        if (suffix) {
          if (selectedText) {
            // Check if text is already formatted
            const beforeText = value.substring(Math.max(0, start - prefix.length), start);
            const afterText = value.substring(end, end + suffix.length);

            if (beforeText === prefix && afterText === suffix) {
              // Remove formatting
              newText = value.substring(0, start - prefix.length) + selectedText + value.substring(end + suffix.length);
              newCursorPos = start - prefix.length + selectedText.length;
            } else {
              // Add formatting
              newText = value.substring(0, start) + prefix + selectedText + suffix + value.substring(end);
              newCursorPos = start + prefix.length + selectedText.length + suffix.length;
            }
          } else {
            // Insert formatting markers at cursor
            newText = value.substring(0, start) + prefix + suffix + value.substring(end);
            newCursorPos = start + prefix.length;
          }
        } else {
          newText = value.substring(0, start) + prefix + value.substring(end);
          newCursorPos = start + prefix.length;
        }
      }

      onChange(newText);
      
      // Restore cursor position after state update
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          textarea.setSelectionRange(newCursorPos, newCursorPos);
        }
      }, 0);
    }, [value, onChange, showMarkdown]);

    const handleToggleMarkdown = React.useCallback(() => {
      setShowMarkdown(!showMarkdown);
      // If switching to WYSIWYG mode, exit preview mode
      if (showMarkdown) {
        setIsPreviewMode(false);
      }
    }, [showMarkdown]);

    const handleFocus = React.useCallback(() => {
      setIsFocused(true);
      setIsPreviewMode(false);
    }, []);

    const handleBlur = React.useCallback((e: React.FocusEvent<HTMLTextAreaElement>) => {
      // Check if the focus is moving to a toolbar button
      const relatedTarget = e.relatedTarget as HTMLElement;
      const isToolbarClick = relatedTarget?.closest('[data-toolbar="true"]');

      if (!isToolbarClick) {
        setIsFocused(false);
        // Show preview if there's content and showPreview is enabled
        // Add a small delay to prevent flickering when clicking toolbar buttons
        setTimeout(() => {
          if (showPreview && value.trim()) {
            setIsPreviewMode(true);
          }
        }, 150);
      }
    }, [showPreview, value]);

    const handleChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange(e.target.value);
    }, [onChange]);

    // Render preview mode
    if (isPreviewMode && value.trim()) {
      return (
        <div
          className={cn(
            "min-h-[32px] w-full rounded-md border border-input bg-background px-3 py-2 text-xs cursor-text",
            className
          )}
          onClick={() => {
            setIsPreviewMode(false);
            setTimeout(() => textareaRef.current?.focus(), 0);
          }}
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              p: ({ children }) => <p className="mb-2 last:mb-0 text-xs whitespace-pre-wrap">{children}</p>,
              ul: ({ children }) => <ul className="mb-2 last:mb-0 pl-4 text-xs">{children}</ul>,
              ol: ({ children }) => <ol className="mb-2 last:mb-0 pl-4 text-xs">{children}</ol>,
              li: ({ children }) => <li className="mb-1 text-xs">{children}</li>,
              strong: ({ children }) => <strong className="font-semibold text-xs">{children}</strong>,
              em: ({ children }) => <em className="italic text-xs">{children}</em>,
              br: () => <br />,
            }}
          >
            {value.replace(/\n/g, '  \n')}
          </ReactMarkdown>
        </div>
      );
    }

    return (
      <div className={cn("space-y-2", className)}>
        {/* Toolbar - only show when focused */}
        {isFocused && (
          <MarkdownToolbar
            onFormat={handleFormat}
            showMarkdown={showMarkdown}
            onToggleMarkdown={handleToggleMarkdown}
            className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
          />
        )}
        
        {/* Textarea or Rendered View */}
        {showMarkdown ? (
          <AutoTextarea
            ref={textareaRef}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
        ) : (
          <div className="relative">
            {/* Hidden textarea for maintaining functionality */}
            <AutoTextarea
              ref={textareaRef}
              value={value}
              onChange={handleChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
              className="absolute inset-0 opacity-0 pointer-events-none"
              {...props}
            />
            {/* Rendered markdown view */}
            <div
              className={cn(
                "min-h-[32px] w-full rounded-md border border-input bg-background px-3 py-2 text-xs cursor-text",
                "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
                props.className
              )}
              onClick={() => textareaRef.current?.focus()}
            >
              {value.trim() ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    p: ({ children }) => <p className="mb-2 last:mb-0 text-xs whitespace-pre-wrap">{children}</p>,
                    ul: ({ children }) => <ul className="mb-2 last:mb-0 pl-4 text-xs">{children}</ul>,
                    ol: ({ children }) => <ol className="mb-2 last:mb-0 pl-4 text-xs">{children}</ol>,
                    li: ({ children }) => <li className="mb-1 text-xs">{children}</li>,
                    strong: ({ children }) => <strong className="font-semibold text-xs">{children}</strong>,
                    em: ({ children }) => <em className="italic text-xs">{children}</em>,
                    br: () => <br />,
                  }}
                >
                  {value.replace(/\n/g, '  \n')}
                </ReactMarkdown>
              ) : (
                <span className="text-muted-foreground text-xs">
                  {props.placeholder || "Start typing..."}
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

MarkdownEditor.displayName = "MarkdownEditor";
