/* Mobile Time Picker Styles */

/* Hide scrollbars for time wheel picker */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Smooth scrolling for time wheels */
.time-wheel-container {
  scroll-behavior: smooth;
  overscroll-behavior: none;
  /* Prevent bouncing on iOS */
  -webkit-overflow-scrolling: touch;
  /* Prevent elastic scrolling */
  overflow-anchor: none;
}

/* Touch-friendly interactions */
.time-wheel-item {
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Prevent text selection during drag */
.time-wheel-dragging {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Enhanced focus styles for accessibility */
.time-wheel-item:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth transitions for wheel items */
.time-wheel-item {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* Selection indicator styling */
.time-wheel-selection-indicator {
  background: linear-gradient(
    to right,
    transparent,
    var(--muted) 20%,
    var(--muted) 80%,
    transparent
  );
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
}

/* Gradient overlays for fade effect */
.time-wheel-gradient-top {
  background: linear-gradient(
    to bottom,
    var(--background) 0%,
    var(--background) 60%,
    transparent 100%
  );
}

.time-wheel-gradient-bottom {
  background: linear-gradient(
    to top,
    var(--background) 0%,
    var(--background) 60%,
    transparent 100%
  );
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  /* Increase touch targets on mobile */
  .time-wheel-item {
    min-height: 48px;
    padding: 8px 12px;
  }
  
  /* Optimize for mobile viewport */
  .mobile-time-picker-content {
    height: 100vh;
    height: 100dvh; /* Use dynamic viewport height when available */
  }
  
  /* Ensure proper spacing for safe areas */
  .mobile-time-picker-buttons {
    padding-bottom: env(safe-area-inset-bottom, 16px);
  }
}

/* Desktop optimizations */
@media (min-width: 768px) {
  .time-wheel-item {
    min-height: 40px;
    padding: 6px 10px;
  }
  
  /* Compact modal on desktop */
  .mobile-time-picker-content {
    max-height: 400px;
    border-radius: 12px;
  }
}

/* High contrast mode support */
@media (forced-colors: active) {
  .time-wheel-selection-indicator {
    border: 2px solid CanvasText;
    background: Canvas;
  }
  
  .time-wheel-item {
    forced-color-adjust: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .time-wheel-item {
    transition: none;
  }
  
  .time-wheel-container {
    scroll-behavior: auto;
  }
}

/* Dark mode specific adjustments */
.dark .time-wheel-gradient-top {
  background: linear-gradient(
    to bottom,
    var(--background) 0%,
    var(--background) 60%,
    transparent 100%
  );
}

.dark .time-wheel-gradient-bottom {
  background: linear-gradient(
    to top,
    var(--background) 0%,
    var(--background) 60%,
    transparent 100%
  );
}

/* Animation for modal entrance */
.mobile-time-picker-enter {
  opacity: 0;
  transform: translateY(100%);
}

.mobile-time-picker-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.mobile-time-picker-exit {
  opacity: 1;
  transform: translateY(0);
}

.mobile-time-picker-exit-active {
  opacity: 0;
  transform: translateY(100%);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Haptic feedback simulation */
.time-wheel-item:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-out;
}

/* Focus management for keyboard navigation */
.time-wheel-container:focus-within .time-wheel-item:focus {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

/* Ensure proper z-index stacking */
.mobile-time-picker-modal {
  z-index: 50;
}

.time-wheel-selection-indicator {
  z-index: 10;
}

.time-wheel-gradient-top,
.time-wheel-gradient-bottom {
  z-index: 20;
}
