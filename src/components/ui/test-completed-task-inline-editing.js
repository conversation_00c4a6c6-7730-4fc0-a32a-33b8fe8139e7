// Manual test script for completed task inline editing functionality
// Run this in the browser console to test inline editing on completed tasks

function testCompletedTaskInlineEditing() {
  console.log('🧪 Testing inline editing functionality for completed tasks...');
  
  // Helper function to wait for a condition
  function waitFor(condition, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        if (condition()) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error('Timeout waiting for condition'));
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  }

  // Test 1: Find completed tasks
  console.log('📋 Step 1: Looking for completed tasks...');
  
  // Look for completed tasks (they should have line-through styling and muted colors)
  const completedTasks = Array.from(document.querySelectorAll('[class*="line-through"], [class*="bg-muted"]'))
    .filter(el => el.closest('[class*="border"]')); // Make sure it's within a task card
  
  console.log(`Found ${completedTasks.length} potential completed task elements`);
  
  if (completedTasks.length === 0) {
    console.log('⚠️ No completed tasks found. Please complete a task first to test inline editing.');
    return;
  }

  // Test 2: Test title inline editing
  console.log('📝 Step 2: Testing title inline editing on completed tasks...');
  
  const completedTaskCards = Array.from(document.querySelectorAll('[class*="bg-muted/50"]'));
  console.log(`Found ${completedTaskCards.length} completed task cards`);
  
  if (completedTaskCards.length > 0) {
    const firstCompletedCard = completedTaskCards[0];
    const titleElement = firstCompletedCard.querySelector('[class*="line-through"]');
    
    if (titleElement) {
      console.log('🎯 Found completed task title element. Testing click to edit...');
      
      // Check if it has cursor-pointer class
      const hasCursorPointer = titleElement.classList.contains('cursor-pointer') || 
                              getComputedStyle(titleElement).cursor === 'pointer';
      
      if (hasCursorPointer) {
        console.log('✅ Title element has cursor pointer - inline editing should be enabled');
        
        // Simulate click
        titleElement.click();
        
        // Wait for input field to appear
        setTimeout(() => {
          const inputField = firstCompletedCard.querySelector('input[type="text"]');
          if (inputField) {
            console.log('✅ Title inline editing works! Input field appeared for completed task');
            
            // Test typing
            inputField.value = 'Test edited title';
            inputField.dispatchEvent(new Event('input', { bubbles: true }));
            
            // Test escape to cancel
            inputField.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', bubbles: true }));
            
            setTimeout(() => {
              const inputStillExists = firstCompletedCard.querySelector('input[type="text"]');
              if (!inputStillExists) {
                console.log('✅ Escape key cancels editing correctly');
              } else {
                console.log('⚠️ Escape key did not cancel editing');
              }
            }, 100);
            
          } else {
            console.log('❌ Title inline editing failed - no input field appeared');
          }
        }, 200);
        
      } else {
        console.log('❌ Title element does not have cursor pointer - inline editing may be disabled');
      }
    }
  }

  // Test 3: Test description inline editing
  setTimeout(() => {
    console.log('📄 Step 3: Testing description inline editing on completed tasks...');
    
    if (completedTaskCards.length > 0) {
      const firstCompletedCard = completedTaskCards[0];
      const descriptionElements = Array.from(firstCompletedCard.querySelectorAll('p'))
        .filter(p => p.textContent.includes('Add a description') || 
                    (p.textContent.length > 0 && !p.textContent.includes('Add due date')));
      
      if (descriptionElements.length > 0) {
        const descElement = descriptionElements[0];
        console.log('🎯 Found description element. Testing click to edit...');
        
        const hasCursorPointer = descElement.classList.contains('cursor-pointer') || 
                                getComputedStyle(descElement).cursor === 'pointer';
        
        if (hasCursorPointer) {
          console.log('✅ Description element has cursor pointer - inline editing should be enabled');
          
          descElement.click();
          
          setTimeout(() => {
            const textareaField = firstCompletedCard.querySelector('textarea');
            if (textareaField) {
              console.log('✅ Description inline editing works! Textarea appeared for completed task');
            } else {
              console.log('❌ Description inline editing failed - no textarea appeared');
            }
          }, 200);
          
        } else {
          console.log('❌ Description element does not have cursor pointer');
        }
      }
    }
  }, 1000);

  // Test 4: Test due date inline editing
  setTimeout(() => {
    console.log('📅 Step 4: Testing due date inline editing on completed tasks...');
    
    if (completedTaskCards.length > 0) {
      const firstCompletedCard = completedTaskCards[0];
      const dateElements = Array.from(firstCompletedCard.querySelectorAll('*'))
        .filter(el => el.textContent && (
          el.textContent.includes('Add due date') ||
          el.textContent.match(/\d{1,2}\/\d{1,2}\/\d{4}/) ||
          el.textContent.match(/\w+ \d{1,2}, \d{4}/)
        ));
      
      if (dateElements.length > 0) {
        const dateElement = dateElements[0];
        console.log('🎯 Found date element. Testing click to edit...');
        
        const hasCursorPointer = dateElement.classList.contains('cursor-pointer') || 
                                getComputedStyle(dateElement).cursor === 'pointer';
        
        if (hasCursorPointer) {
          console.log('✅ Date element has cursor pointer - inline editing should be enabled');
          
          dateElement.click();
          
          setTimeout(() => {
            const datePickerDialog = document.querySelector('[role="dialog"]');
            if (datePickerDialog) {
              console.log('✅ Due date inline editing works! Date picker appeared for completed task');
            } else {
              console.log('❌ Due date inline editing failed - no date picker appeared');
            }
          }, 200);
          
        } else {
          console.log('❌ Date element does not have cursor pointer');
        }
      }
    }
  }, 2000);

  // Test 5: Test tag inline editing
  setTimeout(() => {
    console.log('🏷️ Step 5: Testing tag inline editing on completed tasks...');
    
    if (completedTaskCards.length > 0) {
      const firstCompletedCard = completedTaskCards[0];
      const tagElements = firstCompletedCard.querySelectorAll('[data-tag-pill]');
      
      if (tagElements.length > 0) {
        const firstTag = tagElements[0];
        console.log('🎯 Found tag element. Testing click to edit...');
        
        const hasCursorPointer = firstTag.classList.contains('cursor-pointer') || 
                                getComputedStyle(firstTag).cursor === 'pointer';
        
        if (hasCursorPointer) {
          console.log('✅ Tag element has cursor pointer - inline editing should be enabled');
          
          firstTag.click();
          
          setTimeout(() => {
            const tagInputField = firstTag.querySelector('input');
            if (tagInputField) {
              console.log('✅ Tag inline editing works! Input field appeared for completed task tag');
            } else {
              console.log('❌ Tag inline editing failed - no input field appeared');
            }
          }, 200);
          
        } else {
          console.log('❌ Tag element does not have cursor pointer');
        }
      } else {
        console.log('ℹ️ No tags found on completed tasks to test');
      }
    }
  }, 3000);

  // Test 6: Test adding new tags to completed tasks
  setTimeout(() => {
    console.log('➕ Step 6: Testing adding new tags to completed tasks...');
    
    if (completedTaskCards.length > 0) {
      const firstCompletedCard = completedTaskCards[0];
      const addTagButton = firstCompletedCard.querySelector('[data-inline-tag-picker]');
      
      if (addTagButton) {
        console.log('✅ Add tag button found on completed task - tag adding should be enabled');
        
        addTagButton.click();
        
        setTimeout(() => {
          const tagPickerPopover = document.querySelector('[role="dialog"]');
          if (tagPickerPopover) {
            console.log('✅ Tag adding works! Tag picker opened for completed task');
          } else {
            console.log('❌ Tag adding failed - no tag picker appeared');
          }
        }, 200);
        
      } else {
        console.log('❌ Add tag button not found on completed task');
      }
    }
  }, 4000);

  console.log('🏁 Test completed! Check the results above.');
}

// Instructions for running the test:
console.log(`
🧪 Completed Task Inline Editing Test

To run this test:
1. Navigate to the tasks page (/tasks)
2. Make sure you have at least one completed task
3. Open the browser console (F12)
4. Run: testCompletedTaskInlineEditing()
5. Check the console output for test results

If you don't have completed tasks:
1. Create a task
2. Click the checkbox to mark it as completed
3. Then run the test
`);

// Export for manual execution
window.testCompletedTaskInlineEditing = testCompletedTaskInlineEditing;
