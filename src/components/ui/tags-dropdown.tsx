"use client";

import * as React from "react";
import { Search, Tag as TagIcon, X, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ColorPicker } from "@/components/ui/color-picker";
import { getRandomTagColor } from "@/lib/tag-colors";
import { useSmartTagSearch } from "@/hooks/use-smart-tag-search";
import { useMediaQuery } from "@/hooks/use-media-query";
import { MobileTagPicker } from "@/components/ui/mobile-tag-picker";
import type { Tag } from "@/lib/db";
import { TagFilter } from "@/lib/types";

export interface TagsDropdownProps {
  availableTags: Tag[];
  onTagSelect: (tag: TagFilter) => void;
  onTagCreate: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags: (searchTerm: string) => Promise<Tag[]>;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  activeTag?: TagFilter | null;
  onClearFilter?: () => void;
}

export function TagsDropdown({
  availableTags,
  onTagSelect,
  onTagCreate,
  onSearchTags,
  className,
  disabled = false,
  placeholder = "Filter by tag...",
  activeTag = null,
  onClearFilter,
}: TagsDropdownProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isCreating, setIsCreating] = React.useState(false);
  const [newTagColor, setNewTagColor] = React.useState<string>(getRandomTagColor().value);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Use smart search hook for optimal performance
  const { filteredTags, isSearching, searchTerm, setSearchTerm } = useSmartTagSearch({
    availableTags,
    selectedTags: [], // No selected tags to filter out in this dropdown
    onSearchTags,
    clientSearchThreshold: 2, // Use client search for 1-2 characters
    debounceMs: 300,
  });

  const handleTagSelect = (tag: Tag) => {
    const tagFilter: TagFilter = {
      id: tag.id,
      name: tag.name,
      color: tag.color,
    };
    onTagSelect(tagFilter);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleTagRemove = (tag: Tag) => {
    // This is for the mobile picker compatibility - not used in filter mode
  };

  const handleCreateTag = async () => {
    if (!searchTerm.trim()) return;

    setIsCreating(true);
    try {
      const newTag = await onTagCreate(searchTerm.trim(), newTagColor);
      if (newTag) {
        handleTagSelect(newTag);
        setNewTagColor(getRandomTagColor().value);
      }
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleClearFilter = () => {
    if (onClearFilter) {
      onClearFilter();
    }
    setIsOpen(false);
    setSearchTerm("");
  };

  // Reset search when modal closes
  React.useEffect(() => {
    if (!isOpen) {
      setSearchTerm("");
      setIsCreating(false);
      setNewTagColor(getRandomTagColor().value);
    }
  }, [isOpen, setSearchTerm]);

  const showCreateOption = searchTerm.trim() &&
    !filteredTags.some(tag => tag.name.toLowerCase() === searchTerm.toLowerCase()) &&
    !isSearching;

  // Mobile version - use existing MobileTagPicker
  if (!isDesktop) {
    // If there's an active tag, show it as an active tab-like element
    if (activeTag) {
      return (
        <div className={cn("flex-shrink-0 relative", className)}>
          <div className="flex items-center gap-1">
            <button
              type="button"
              onClick={() => setIsOpen(true)}
              className={cn(
                "px-2 py-1.5 text-sm font-semibold rounded-lg transition-colors whitespace-nowrap relative",
                "border border-transparent hover:bg-muted/50 touch-manipulation"
              )}
              style={{
                pointerEvents: 'auto',
                touchAction: 'manipulation',
                WebkitTapHighlightColor: 'transparent'
              }}
              data-tag-filter="true"
              disabled={disabled}
            >
              <span
                className="truncate max-w-[120px] tab-with-underline"
                style={{ color: activeTag.color }}
              >
                {activeTag.name}
              </span>
            </button>
            <div className="flex-shrink-0">
              <X
                className="h-3 w-3 opacity-60 hover:opacity-100 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearFilter();
                }}
              />
            </div>
          </div>

          {/* Mobile Tag Picker Modal - reuse existing component */}
          <MobileTagPicker
            selectedTags={[]} // Empty for filter mode
            availableTags={availableTags}
            onTagSelect={handleTagSelect}
            onTagRemove={handleTagRemove}
            onTagCreate={onTagCreate}
            onSearchTags={onSearchTags}
            placeholder={placeholder}
            disabled={disabled}
            open={isOpen}
            onOpenChange={setIsOpen}
          />
        </div>
      );
    }

    // If no active tag, show the dropdown
    return (
      <div className={cn("", className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(true)}
          disabled={disabled}
          className="flex items-center gap-2 text-gray-600 hover:text-foreground touch-manipulation"
          style={{
            pointerEvents: 'auto',
            touchAction: 'manipulation',
            WebkitTapHighlightColor: 'transparent'
          }}
        >
          <TagIcon className="h-4 w-4" />
          <span>Tags</span>
        </Button>

        {/* Mobile Tag Picker Modal - reuse existing component */}
        <MobileTagPicker
          selectedTags={[]} // Empty for filter mode
          availableTags={availableTags}
          onTagSelect={handleTagSelect}
          onTagRemove={handleTagRemove}
          onTagCreate={onTagCreate}
          onSearchTags={onSearchTags}
          placeholder={placeholder}
          disabled={disabled}
          open={isOpen}
          onOpenChange={setIsOpen}
        />
      </div>
    );
  }

  // Desktop version
  // If there's an active tag, show it as an active tab-like element
  if (activeTag) {
    return (
      <div className={cn("flex-shrink-0 relative", className)}>
        <div className="flex items-center gap-1">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <button
                type="button"
                className={cn(
                  "px-2 py-1.5 text-sm font-semibold rounded-lg transition-colors whitespace-nowrap relative",
                  "border border-transparent hover:bg-muted/50"
                )}
                data-tag-filter="true"
                disabled={disabled}
              >
                <span
                  className="truncate max-w-[120px] tab-with-underline"
                  style={{ color: activeTag.color }}
                >
                  {activeTag.name}
                </span>
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
              <div className="p-3 space-y-3">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={placeholder}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9"
                    disabled={disabled}
                  />
                </div>

                {/* Clear Filter Option */}
                <div className="border-b pb-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start h-auto p-2"
                    onClick={handleClearFilter}
                  >
                    <X className="mr-2 h-4 w-4" />
                    <span>Clear filter</span>
                  </Button>
                </div>

                {/* Create New Tag Option */}
                {showCreateOption && (
                  <div className="border-b pb-3">
                    <div className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50">
                      <ColorPicker
                        value={newTagColor}
                        onChange={setNewTagColor}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 justify-start h-auto p-1"
                        onClick={handleCreateTag}
                        disabled={isCreating}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Create "{searchTerm}"
                      </Button>
                    </div>
                  </div>
                )}

                {/* Existing Tags */}
                <div className="max-h-48 overflow-y-auto">
                  {isSearching ? (
                    <div className="text-center py-4 text-muted-foreground">
                      Searching...
                    </div>
                  ) : filteredTags.length > 0 ? (
                    <div className="space-y-1">
                      {filteredTags.map((tag) => (
                        <Button
                          key={tag.id}
                          variant="ghost"
                          size="sm"
                          className="w-full justify-start h-auto p-2"
                          onClick={() => handleTagSelect(tag)}
                          disabled={disabled}
                        >
                          <div
                            className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                            style={{ backgroundColor: tag.color }}
                          />
                          <span className="truncate">{tag.name}</span>
                        </Button>
                      ))}
                    </div>
                  ) : searchTerm ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No tags found
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      Start typing to search or create tags
                    </div>
                  )}
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <div className="flex-shrink-0">
            <X
              className="h-3 w-3 opacity-60 hover:opacity-100 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleClearFilter();
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // If no active tag, show the dropdown
  return (
    <div className={cn("", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            disabled={disabled}
            className="flex items-center gap-2 text-gray-600 hover:!text-foreground hover:!bg-muted/50 transition-colors rounded-lg"
          >
            <TagIcon className="h-4 w-4" />
            <span>Tags</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-3 space-y-3">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={placeholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
                disabled={disabled}
              />
            </div>

            {/* Clear Filter Option */}
            {activeTag && (
              <div className="border-b pb-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start h-auto p-2"
                  onClick={handleClearFilter}
                >
                  <X className="mr-2 h-4 w-4" />
                  <span>Clear filter</span>
                </Button>
              </div>
            )}

            {/* Create New Tag Option */}
            {showCreateOption && (
              <div className="border-b pb-3">
                <div className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50">
                  <ColorPicker
                    value={newTagColor}
                    onChange={setNewTagColor}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="flex-1 justify-start h-auto p-1"
                    onClick={handleCreateTag}
                    disabled={isCreating}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create "{searchTerm}"
                  </Button>
                </div>
              </div>
            )}

            {/* Existing Tags */}
            <div className="max-h-48 overflow-y-auto">
              {isSearching ? (
                <div className="text-center py-4 text-muted-foreground">
                  Searching...
                </div>
              ) : filteredTags.length > 0 ? (
                <div className="space-y-1">
                  {filteredTags.map((tag) => (
                    <Button
                      key={tag.id}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-auto p-2"
                      onClick={() => handleTagSelect(tag)}
                      disabled={disabled}
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="truncate">{tag.name}</span>
                    </Button>
                  ))}
                </div>
              ) : searchTerm ? (
                <div className="text-center py-4 text-muted-foreground">
                  No tags found
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Start typing to search or create tags
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
