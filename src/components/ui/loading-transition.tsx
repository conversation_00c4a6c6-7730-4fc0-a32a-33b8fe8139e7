"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { LoadingContainer } from "./loading-spinner";
import { SkeletonCard } from "./skeleton";

interface LoadingTransitionProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  variant?: "spinner" | "skeleton";
  className?: string;
  loadingText?: string;
  transitionDuration?: number;
  minLoadingTime?: number;
}

export function LoadingTransition({
  isLoading,
  children,
  fallback,
  variant = "spinner",
  className,
  loadingText = "Loading...",
  transitionDuration = 300,
  minLoadingTime = 300
}: LoadingTransitionProps) {
  const [showLoading, setShowLoading] = useState(isLoading);
  const [showContent, setShowContent] = useState(!isLoading);
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isLoading) {
      // Starting to load
      setLoadingStartTime(Date.now());
      setShowContent(false);
      
      // Small delay to prevent flashing for very quick loads
      timer = setTimeout(() => {
        setShowLoading(true);
      }, 50);
    } else {
      // Finished loading
      const now = Date.now();
      const elapsed = loadingStartTime ? now - loadingStartTime : 0;
      const remainingMinTime = Math.max(0, minLoadingTime - elapsed);

      // Ensure minimum loading time, then transition to content
      timer = setTimeout(() => {
        setShowLoading(false);
        
        // Small delay for smooth transition
        setTimeout(() => {
          setShowContent(true);
        }, transitionDuration / 2);
      }, remainingMinTime);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading, loadingStartTime, minLoadingTime, transitionDuration]);

  const defaultFallback = variant === "skeleton" ? (
    <SkeletonCard />
  ) : (
    <LoadingContainer text={loadingText} />
  );

  return (
    <div className={cn("relative", className)}>
      {/* Loading State */}
      <div
        className={cn(
          "transition-opacity duration-300",
          showLoading ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        style={{
          transitionDuration: `${transitionDuration}ms`
        }}
      >
        {fallback || defaultFallback}
      </div>

      {/* Content State */}
      <div
        className={cn(
          "transition-opacity duration-300",
          showContent ? "opacity-100" : "opacity-0 pointer-events-none",
          showLoading ? "absolute inset-0" : ""
        )}
        style={{
          transitionDuration: `${transitionDuration}ms`
        }}
      >
        {children}
      </div>
    </div>
  );
}

interface StaggeredLoadingProps {
  items: Array<{
    isLoading: boolean;
    content: React.ReactNode;
    fallback?: React.ReactNode;
  }>;
  staggerDelay?: number;
  className?: string;
}

export function StaggeredLoading({
  items,
  staggerDelay = 100,
  className
}: StaggeredLoadingProps) {
  const [visibleItems, setVisibleItems] = useState<number>(0);

  useEffect(() => {
    // Reset when items change
    setVisibleItems(0);

    // Stagger the appearance of items
    items.forEach((_, index) => {
      setTimeout(() => {
        setVisibleItems(prev => Math.max(prev, index + 1));
      }, index * staggerDelay);
    });
  }, [items, staggerDelay]);

  return (
    <div className={cn("space-y-4", className)}>
      {items.map((item, index) => (
        <div
          key={index}
          className={cn(
            "transition-all duration-300",
            index < visibleItems 
              ? "opacity-100 translate-y-0" 
              : "opacity-0 translate-y-4"
          )}
        >
          <LoadingTransition
            isLoading={item.isLoading}
            fallback={item.fallback}
          >
            {item.content}
          </LoadingTransition>
        </div>
      ))}
    </div>
  );
}

interface ProgressiveLoadingProps {
  stages: Array<{
    isLoading: boolean;
    content: React.ReactNode;
    priority: number;
  }>;
  className?: string;
}

export function ProgressiveLoading({
  stages,
  className
}: ProgressiveLoadingProps) {
  // Sort stages by priority (lower number = higher priority)
  const sortedStages = [...stages].sort((a, b) => a.priority - b.priority);

  return (
    <div className={cn("space-y-4", className)}>
      {sortedStages.map((stage, index) => (
        <LoadingTransition
          key={index}
          isLoading={stage.isLoading}
          transitionDuration={400}
        >
          {stage.content}
        </LoadingTransition>
      ))}
    </div>
  );
}
