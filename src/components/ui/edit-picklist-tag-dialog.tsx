"use client";

import * as React from "react";
import { ChevronLeft, Plus, X, GripVertical } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ColorPicker } from "@/components/ui/color-picker";
import type { Tag, PicklistValue } from "@/lib/db";
import type { UpdatePicklistValuesData } from "@/lib/types";

export interface EditPicklistTagDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tag: Tag | null;
  picklistValues: PicklistValue[];
  onTagUpdate: (tagId: string, name: string, color: string) => Promise<boolean>;
  onPicklistValuesUpdate: (data: UpdatePicklistValuesData) => Promise<boolean>;
  disabled?: boolean;
}

interface PicklistValueItem {
  id?: string;
  value: string;
  position: number;
  isNew?: boolean;
}

export function EditPicklistTagDialog({
  open,
  onOpenChange,
  tag,
  picklistValues,
  onTagUpdate,
  onPicklistValuesUpdate,
  disabled = false,
}: EditPicklistTagDialogProps) {
  const [name, setName] = React.useState("");
  const [color, setColor] = React.useState("#3b82f6");
  const [values, setValues] = React.useState<PicklistValueItem[]>([]);
  const [isSaving, setIsSaving] = React.useState(false);
  const [error, setError] = React.useState("");

  // Initialize form when tag or picklistValues change
  React.useEffect(() => {
    if (tag && open) {
      setName(tag.name);
      setColor(tag.color);
      
      // Convert picklist values to editable format
      const sortedValues = [...picklistValues].sort((a, b) => a.position - b.position);
      const valueItems: PicklistValueItem[] = sortedValues.map(pv => ({
        id: pv.id,
        value: pv.value,
        position: pv.position,
        isNew: false,
      }));
      
      // Ensure at least one value
      if (valueItems.length === 0) {
        valueItems.push({ value: '', position: 0, isNew: true });
      }
      
      setValues(valueItems);
      setError("");
    }
  }, [tag, picklistValues, open]);

  const handleClose = () => {
    if (!isSaving) {
      onOpenChange(false);
    }
  };

  const handleValueChange = (index: number, newValue: string) => {
    const newValues = [...values];
    newValues[index].value = newValue;
    setValues(newValues);
  };

  const handleAddValue = () => {
    const newPosition = Math.max(...values.map(v => v.position), -1) + 1;
    setValues([...values, { 
      value: '', 
      position: newPosition, 
      isNew: true 
    }]);
  };

  const handleRemoveValue = (index: number) => {
    if (values.length > 1) {
      const newValues = values.filter((_, i) => i !== index);
      setValues(newValues);
    }
  };

  const handleSave = async () => {
    if (!tag) return;

    if (!name.trim()) {
      setError("Tag name is required");
      return;
    }

    const validValues = values.filter(v => v.value.trim());
    if (validValues.length === 0) {
      setError("At least one picklist value is required");
      return;
    }

    setIsSaving(true);
    setError("");

    try {
      // Update tag name and color if changed
      if (name.trim() !== tag.name || color !== tag.color) {
        const tagUpdateSuccess = await onTagUpdate(tag.id, name.trim(), color);
        if (!tagUpdateSuccess) {
          setError("Failed to update tag. Tag name may already exist.");
          setIsSaving(false);
          return;
        }
      }

      // Update picklist values
      const valuesData: UpdatePicklistValuesData = {
        tagId: tag.id,
        values: validValues.map((v, index) => ({
          id: v.isNew ? undefined : v.id,
          value: v.value.trim(),
          position: index,
        })),
      };

      const valuesUpdateSuccess = await onPicklistValuesUpdate(valuesData);
      if (!valuesUpdateSuccess) {
        setError("Failed to update picklist values");
        setIsSaving(false);
        return;
      }

      onOpenChange(false);
    } catch (error) {
      console.error("Error updating picklist tag:", error);
      setError("Failed to update tag");
    } finally {
      setIsSaving(false);
    }
  };

  if (!tag) return null;

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Edit Picklist Tag</MobileDialogTitle>
        </VisuallyHidden>
        
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
            disabled={isSaving}
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4 space-y-6">
          {/* Tag Name */}
          <div className="space-y-2">
            <Label htmlFor="tag-name">Tag Name</Label>
            <Input
              id="tag-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter tag name"
              disabled={isSaving || disabled}
              autoComplete="off"
              spellCheck="false"
              maxLength={50}
            />
          </div>

          {/* Color Picker */}
          <div className="space-y-2">
            <Label>Color</Label>
            <div className="flex items-center gap-3">
              <ColorPicker
                value={color}
                onChange={setColor}
                disabled={isSaving || disabled}
              />
              <span className="text-sm text-muted-foreground">
                Choose a color for this tag
              </span>
            </div>
          </div>

          {/* Picklist Values */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Picklist Values</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddValue}
                disabled={isSaving || disabled}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Value
              </Button>
            </div>
            
            <div className="space-y-2">
              {values.map((value, index) => (
                <div key={`${value.id || 'new'}-${index}`} className="flex items-center gap-2">
                  <GripVertical className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <Input
                    value={value.value}
                    onChange={(e) => handleValueChange(index, e.target.value)}
                    placeholder={`Value ${index + 1}`}
                    disabled={isSaving || disabled}
                    className="flex-1"
                    maxLength={50}
                  />
                  {values.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveValue(index)}
                      disabled={isSaving || disabled}
                      className="p-1 h-8 w-8"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-lg">
              {error}
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="px-4 pb-4 border-t bg-background">
          <Button
            onClick={handleSave}
            disabled={isSaving || disabled || !name.trim()}
            className="w-full mt-4"
          >
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
