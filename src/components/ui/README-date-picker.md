# DatePicker Component

A standardized, accessible date picker component built with shadcn/ui components.

## Features

- Built with shadcn/ui components for consistent styling
- Accessible by default with proper ARIA attributes
- Keyboard navigation support
- Screen reader friendly
- Calendar popover interface with shadcn/ui Calendar component
- Selected dates display as pill/badge with integrated clear button
- Today button in calendar popover for easy date selection
- Proper event handling to prevent conflicts
- Responsive design
- Support for disabled state
- Error state handling
- No visible labels (screen reader accessible labels only)
- Optimized re-rendering with React.useCallback and proper key props

## Usage

```tsx
import { DatePicker } from "@/components/ui/date-picker";
import { useState } from "react";

export default function MyComponent() {
  const [date, setDate] = useState<Date | undefined>(undefined);

  return (
    <DatePicker
      date={date}
      setDate={setDate}
      placeholder="Select a date"
      label="Due Date"
      showInputField={true}
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `date` | `Date \| undefined` | Required | The currently selected date |
| `setDate` | `(date: Date \| undefined) => void` | Required | Function to update the selected date |
| `className` | `string` | `undefined` | Additional CSS classes to apply to the component |
| `placeholder` | `string` | `"Pick a date"` | Placeholder text when no date is selected |
| `disabled` | `boolean` | `false` | Whether the date picker is disabled |
| `showInputField` | `boolean` | `false` | Whether to show a text input field for direct date entry |
| `label` | `string` | `undefined` | Label for the date picker |
| `error` | `string` | `undefined` | Error message to display |

## Accessibility

The DatePicker component follows accessibility best practices:

- Proper ARIA attributes for screen readers
- Keyboard navigation support
- Focus management
- High contrast mode support
- Clear visual feedback for selected dates
- Error states with descriptive messages

### Keyboard Navigation

- `Tab`: Move focus to the date picker button
- `Enter` or `Space`: Open the date picker popover
- `Escape`: Close the date picker popover
- `Arrow keys`: Navigate between days in the calendar
- `Home`: Go to the first day of the month
- `End`: Go to the last day of the month
- `Page Up`: Go to the previous month
- `Page Down`: Go to the next month
- `Enter` or `Space`: Select the focused day

## Styling

The DatePicker component uses CSS variables for styling, which can be customized in your application's CSS:

```css
:root {
  --rdp-cell-size: 40px;
  --rdp-accent-color: var(--primary);
  --rdp-background-color: var(--primary);
  --rdp-accent-color-dark: var(--primary);
  --rdp-background-color-dark: var(--primary);
  --rdp-outline: 2px solid var(--ring);
  --rdp-outline-selected: 2px solid var(--primary);
}
```

## Examples

### Basic Usage

```tsx
<DatePicker
  date={date}
  setDate={setDate}
/>
```

### With Label and Input Field

```tsx
<DatePicker
  date={date}
  setDate={setDate}
  label="Event Date"
  showInputField={true}
/>
```

### With Error State

```tsx
<DatePicker
  date={date}
  setDate={setDate}
  error="Please select a future date"
/>
```

### Disabled State

```tsx
<DatePicker
  date={date}
  setDate={setDate}
  disabled={true}
/>
```

## Implementation Details

The DatePicker component uses:

- [shadcn/ui](https://ui.shadcn.com/) components (Button, Input, Label) for consistent styling
- [date-fns](https://date-fns.org/) for date manipulation and formatting
- HTML5 date input for native browser date picker support
- [Lucide React](https://lucide.dev/) for icons
