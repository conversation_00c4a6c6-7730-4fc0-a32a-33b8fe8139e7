"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>ontainer, LoadingSpinner } from "./loading-spinner";
import { Skeleton, SkeletonCard, SkeletonList, SkeletonDesktopLayout } from "./skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSidebar } from "@/contexts/sidebar-context";
import { useChatSidebar } from "@/contexts/chat-sidebar-context";

interface LoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  variant?: "spinner" | "skeleton";
  loadingText?: string;
  error?: Error | null;
  errorFallback?: React.ReactNode;
}

export function LoadingBoundary({
  isLoading,
  children,
  fallback,
  className,
  variant = "spinner",
  loadingText = "Loading...",
  error,
  errorFallback
}: LoadingBoundaryProps) {
  // Error state
  if (error) {
    if (errorFallback) {
      return <>{errorFallback}</>;
    }
    
    return (
      <div className={cn("flex flex-col items-center justify-center py-8 text-center", className)}>
        <div className="text-destructive mb-2">⚠️</div>
        <p className="text-sm text-muted-foreground">
          Something went wrong. Please try again.
        </p>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (variant === "skeleton") {
      return (
        <div className={cn("animate-in fade-in-0 duration-300", className)}>
          <SkeletonList items={3} />
        </div>
      );
    }

    return (
      <div className={cn("animate-in fade-in-0 duration-300", className)}>
        <LoadingContainer text={loadingText} />
      </div>
    );
  }

  // Success state with smooth transition
  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}

interface PageLoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  title?: string;
  className?: string;
}

export function PageLoadingBoundary({
  isLoading,
  children,
  title,
  className
}: PageLoadingBoundaryProps) {
  if (isLoading) {
    return (
      <div className={cn("px-2 md:px-4 py-6 space-y-6", className)}>
        {title && (
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
        )}
        <div className="grid gap-4">
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}

interface QueryLoadingBoundaryProps {
  queries: Array<{ isLoading: boolean; error?: Error | null }>;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  loadingText?: string;
}

export function QueryLoadingBoundary({
  queries,
  children,
  fallback,
  className,
  loadingText = "Loading..."
}: QueryLoadingBoundaryProps) {
  const isLoading = queries.some(query => query.isLoading);
  const error = queries.find(query => query.error)?.error;

  return (
    <LoadingBoundary
      isLoading={isLoading}
      error={error}
      fallback={fallback}
      className={className}
      loadingText={loadingText}
    >
      {children}
    </LoadingBoundary>
  );
}

interface InlineLoadingProps {
  isLoading: boolean;
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

export function InlineLoading({
  isLoading,
  size = "sm",
  text,
  className
}: InlineLoadingProps) {
  if (!isLoading) return null;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <LoadingSpinner size={size} />
      {text && (
        <span className="text-sm text-muted-foreground font-light">
          {text}
        </span>
      )}
    </div>
  );
}

interface MobilePageLoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}

export function MobilePageLoadingBoundary({
  isLoading,
  children,
  className
}: MobilePageLoadingBoundaryProps) {
  if (isLoading) {
    return (
      <div className={cn("min-h-screen flex flex-col", className)}>
        {/* Mobile Unified Header Skeleton */}
        <SkeletonUnifiedHeader isDesktop={false} />

        {/* Main Content Skeleton */}
        <main className="flex-1 pb-16">
          <div className="px-2 py-3 space-y-6">
            {/* Main Card Container */}
            <div className="gradient-border-muted rounded-lg">
              <div className="p-6 space-y-4">
                {/* Task Cards */}
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </div>
            </div>
          </div>
        </main>

        {/* Mobile Bottom Navigation Skeleton */}
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t border-border">
          <div className="flex justify-around items-center h-16 px-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex flex-col items-center gap-1">
                <Skeleton className="h-6 w-6 rounded" />
                <Skeleton className="h-3 w-8" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

interface DesktopPageLoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}

export function DesktopPageLoadingBoundary({
  isLoading,
  children,
  className
}: DesktopPageLoadingBoundaryProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed } = useSidebar();
  const { isOpen: isChatOpen, width: chatWidth } = useChatSidebar();

  if (isLoading && isDesktop) {
    return (
      <SkeletonDesktopLayout
        isCollapsed={isCollapsed}
        isChatOpen={isChatOpen}
        chatWidth={chatWidth}
        className={className}
      />
    );
  }

  if (isLoading) {
    // Use dedicated mobile loading boundary
    return (
      <MobilePageLoadingBoundary
        isLoading={true}
        className={className}
      >
        {children}
      </MobilePageLoadingBoundary>
    );
  }

  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}
