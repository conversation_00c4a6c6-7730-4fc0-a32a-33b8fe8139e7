"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { AlertTriangle, RefreshCw } from "lucide-react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; retry?: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  className?: string;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return (
        <div className={this.props.className}>
          <FallbackComponent error={this.state.error} retry={this.retry} />
        </div>
      );
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  retry?: () => void;
  className?: string;
}

export function DefaultErrorFallback({ error, retry, className }: ErrorFallbackProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center py-12 px-4 text-center",
      className
    )}>
      <div className="text-destructive mb-4">
        <AlertTriangle className="h-12 w-12" />
      </div>
      <h2 className="text-xl font-semibold mb-2">Something went wrong</h2>
      <p className="text-muted-foreground mb-4 max-w-md">
        {error?.message || "An unexpected error occurred. Please try again."}
      </p>
      {retry && (
        <Button onClick={retry} variant="outline" className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  );
}

export function MinimalErrorFallback({ error, retry, className }: ErrorFallbackProps) {
  return (
    <div className={cn(
      "flex items-center justify-center py-8 text-center",
      className
    )}>
      <div className="flex flex-col items-center gap-2">
        <AlertTriangle className="h-6 w-6 text-destructive" />
        <p className="text-sm text-muted-foreground">
          {error?.message || "Something went wrong"}
        </p>
        {retry && (
          <Button onClick={retry} variant="ghost" size="sm" className="gap-1">
            <RefreshCw className="h-3 w-3" />
            Retry
          </Button>
        )}
      </div>
    </div>
  );
}

interface QueryErrorBoundaryProps {
  children: React.ReactNode;
  error?: Error | null;
  isLoading?: boolean;
  retry?: () => void;
  className?: string;
  fallback?: React.ComponentType<ErrorFallbackProps>;
}

export function QueryErrorBoundary({
  children,
  error,
  isLoading,
  retry,
  className,
  fallback: FallbackComponent = MinimalErrorFallback
}: QueryErrorBoundaryProps) {
  if (error && !isLoading) {
    return (
      <div className={className}>
        <FallbackComponent error={error} retry={retry} />
      </div>
    );
  }

  return <>{children}</>;
}

// Hook for handling async errors in components
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    console.error("Async error:", error);
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  const retry = React.useCallback(() => {
    setError(null);
    // Return a function that can be used to retry the operation
    return clearError;
  }, [clearError]);

  return {
    error,
    handleError,
    clearError,
    retry,
    hasError: error !== null
  };
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
