"use client";

import { useState, useEffect } from "react";
import { X, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";

interface UsageBannerProps {
  message: string;
  show: boolean;
  onDismiss?: () => void;
  autoHideDelay?: number; // in milliseconds, default 5000 (5 seconds)
  className?: string;
}

export function UsageBanner({ 
  message, 
  show, 
  onDismiss, 
  autoHideDelay = 5000,
  className 
}: UsageBannerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      setIsAnimating(true);
      
      // Auto-hide after delay
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [show, autoHideDelay]);

  const handleDismiss = () => {
    setIsAnimating(false);
    
    // Wait for fade-out animation to complete before hiding
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, 300);
  };

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 bg-destructive text-destructive-foreground shadow-lg transition-all duration-300 ease-in-out",
        isAnimating 
          ? "translate-y-0 opacity-100" 
          : "-translate-y-full opacity-0",
        className
      )}
    >
      <div className="container-max-width px-4 py-3">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2 flex-1">
            <AlertTriangle className="h-5 w-5 flex-shrink-0" />
            <p className="text-sm font-medium">{message}</p>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="h-8 w-8 p-0 hover:bg-destructive-foreground/10"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Dismiss</span>
          </Button>
        </div>
      </div>
    </div>
  );
}

// Context and hook for managing usage banners globally
import { createContext, useContext, ReactNode } from "react";

interface UsageBannerContextType {
  showUsageBanner: (message: string) => void;
}

const UsageBannerContext = createContext<UsageBannerContextType | undefined>(undefined);

interface UsageBannerProviderProps {
  children: ReactNode;
}

export function UsageBannerProvider({ children }: UsageBannerProviderProps) {
  const [bannerState, setBannerState] = useState<{
    message: string;
    show: boolean;
  }>({
    message: "",
    show: false,
  });

  const showUsageBanner = (message: string) => {
    setBannerState({ message, show: true });
  };

  const handleDismiss = () => {
    setBannerState(prev => ({ ...prev, show: false }));
  };

  return (
    <UsageBannerContext.Provider value={{ showUsageBanner }}>
      {children}
      <UsageBanner
        message={bannerState.message}
        show={bannerState.show}
        onDismiss={handleDismiss}
      />
    </UsageBannerContext.Provider>
  );
}

export function useUsageBanner() {
  const context = useContext(UsageBannerContext);
  if (context === undefined) {
    throw new Error('useUsageBanner must be used within a UsageBannerProvider');
  }
  return context;
}
