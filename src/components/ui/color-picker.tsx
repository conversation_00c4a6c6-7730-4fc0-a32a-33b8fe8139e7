"use client";

import * as React from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { TAG_COLORS, type TagColor } from "@/lib/tag-colors";

export interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  className?: string;
  disabled?: boolean;
}

export function ColorPicker({
  value,
  onChange,
  className,
  disabled = false,
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  
  const selectedColor = TAG_COLORS.find(color => color.value === value);

  const handleColorSelect = (color: TagColor) => {
    onChange(color.value);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-8 w-8 p-0 border-2",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          disabled={disabled}
          aria-label="Select color"
        >
          <div
            className="h-4 w-4 rounded-sm"
            style={{
              backgroundColor: selectedColor?.value || TAG_COLORS[0].value,
            }}
          />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-64 p-3" align="start">
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Choose a color</h4>
          
          <div className="grid grid-cols-4 gap-2">
            {TAG_COLORS.map((color) => (
              <button
                key={color.value}
                type="button"
                className={cn(
                  "relative h-8 w-8 rounded-md border-2 border-transparent transition-all hover:scale-110 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                  value === color.value && "border-foreground ring-2 ring-ring ring-offset-2"
                )}
                style={{ backgroundColor: color.value }}
                onClick={() => handleColorSelect(color)}
                aria-label={color.name}
                title={color.name}
              >
                {value === color.value && (
                  <Check 
                    className="h-4 w-4 absolute inset-0 m-auto text-white drop-shadow-sm" 
                    strokeWidth={3}
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
