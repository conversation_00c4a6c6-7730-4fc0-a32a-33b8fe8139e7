"use client";

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTheme } from "@/hooks/use-theme";

type ThemeValue = "light" | "dark" | "system";

interface ThemeOption {
  value: ThemeValue;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const themeOptions: ThemeOption[] = [
  {
    value: "light",
    label: "Light",
    icon: Sun,
    description: "Light mode",
  },
  {
    value: "dark",
    label: "Dark",
    icon: Moon,
    description: "Dark mode",
  },
  {
    value: "system",
    label: "System",
    icon: Monitor,
    description: "Follow system preference",
  },
];

export function ThemeSelector() {
  const { theme, setTheme, isLoading } = useTheme();

  const handleThemeChange = (value: string) => {
    setTheme(value as ThemeValue);
  };

  const currentTheme = themeOptions.find(option => option.value === theme);

  return (
    <div className="space-y-2">
      <Label htmlFor="theme">Theme</Label>
      <Select
        value={theme}
        onValueChange={handleThemeChange}
        disabled={isLoading}
      >
        <SelectTrigger id="theme" className="w-full">
          <SelectValue>
            {currentTheme && (
              <div className="flex items-center gap-2">
                <currentTheme.icon className="h-4 w-4" />
                <span>{currentTheme.label}</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {themeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center gap-2">
                <option.icon className="h-4 w-4" />
                <div className="flex flex-col">
                  <span>{option.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {option.description}
                  </span>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
