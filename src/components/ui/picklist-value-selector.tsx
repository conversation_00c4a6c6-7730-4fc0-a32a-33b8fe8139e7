"use client";

import * as React from "react";
import { Check, ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import type { PicklistValue, ExtendedPicklistValue } from "@/lib/types";

export interface PicklistValueSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  picklistValues: PicklistValue[];
  selectedValueId?: string | null;
  onValueSelect: (valueId: string) => void;
  onClearSelection?: () => void;
  tagName: string;
  tagColor: string;
  disabled?: boolean;
}

export function PicklistValueSelector({
  open,
  onOpenChange,
  picklistValues,
  selectedValueId,
  onValueSelect,
  onClearSelection,
  tagName,
  tagColor,
  disabled = false,
}: PicklistValueSelectorProps) {
  const handleValueSelect = (valueId: string) => {
    onValueSelect(valueId);
    onOpenChange(false);
  };

  const handleClearSelection = () => {
    if (onClearSelection) {
      onClearSelection();
      onOpenChange(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Select {tagName} Value</MobileDialogTitle>
        </VisuallyHidden>
        
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
            disabled={disabled}
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="flex-1 overflow-y-auto">
          {/* Tag Header */}
          <div className="px-4 pb-4">
            <div className="flex items-center gap-3">
              <div
                className="w-4 h-4 rounded-sm flex-shrink-0"
                style={{ backgroundColor: tagColor }}
              />
              <div>
                <h2 className="text-lg font-semibold">{tagName}</h2>
                <p className="text-sm text-muted-foreground">
                  Select a value for this tag
                </p>
              </div>
            </div>
          </div>

          {/* Values List */}
          <div className="px-4 space-y-2">
            {/* Clear Selection Option */}
            {selectedValueId && onClearSelection && (
              <button
                type="button"
                onClick={handleClearSelection}
                disabled={disabled}
                className={cn(
                  "w-full flex items-center justify-between p-4 rounded-lg border transition-colors text-left",
                  "border-border hover:bg-muted/50",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="w-5 h-5 flex-shrink-0" /> {/* Spacer for alignment */}
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-muted-foreground">
                      Clear selection
                    </div>
                  </div>
                </div>
              </button>
            )}

            {/* Picklist Values */}
            {picklistValues.map((value) => {
              const isSelected = selectedValueId === value.id;
              
              return (
                <button
                  key={value.id}
                  type="button"
                  onClick={() => handleValueSelect(value.id)}
                  disabled={disabled}
                  className={cn(
                    "w-full flex items-center justify-between p-4 rounded-lg border transition-colors text-left",
                    isSelected
                      ? "border-primary/30 bg-primary/5"
                      : "border-border hover:bg-muted/50",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    <div className="w-5 h-5 flex-shrink-0 flex items-center justify-center">
                      {isSelected && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className={cn(
                        "font-medium truncate",
                        isSelected && "font-semibold"
                      )}>
                        {value.value}
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}

            {/* Empty State */}
            {picklistValues.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  No values available for this tag
                </p>
              </div>
            )}
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
