import React, { ReactNode } from 'react';
import { cn } from "@/lib/utils";

interface SpotlightCardProps {
  children: ReactNode;
  className?: string;
  listColor?: string;
  enableSpotlight?: boolean;
  spotlightIntensity?: 'subtle' | 'medium' | 'strong';
  style?: React.CSSProperties;
}

const SpotlightCard: React.FC<SpotlightCardProps> = ({
  children,
  className = '',
  listColor,
  enableSpotlight = true,
  spotlightIntensity = 'subtle',
  style
}) => {
  const getSpotlightIntensity = () => {
    switch (spotlightIntensity) {
      case 'subtle': return { bgOpacity: 0.22, borderOpacity: 0.35 };
      case 'medium': return { bgOpacity: 0.30, borderOpacity: 0.45 };
      case 'strong': return { bgOpacity: 0.40, borderOpacity: 0.55 };
      default: return { bgOpacity: 0.22, borderOpacity: 0.35 };
    }
  };

  const { bgOpacity, borderOpacity } = getSpotlightIntensity();

  // Determine spotlight color based on list color or default theme
  const getSpotlightColor = () => {
    if (listColor) {
      return listColor;
    }
    return 'hsl(220, 70%, 60%)'; // Default blue
  };

  const spotlightColor = getSpotlightColor();

  // Create static gradient background that scales with container size
  const getStaticSpotlightBackground = () => {
    if (!enableSpotlight) return {};

    return {
      background: `
        radial-gradient(ellipse 180% 120% at 50% 30%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 100)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 70)}%, transparent) 40%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(bgOpacity * 40)}%, transparent) 70%,
          transparent 100%)
      `
    };
  };

  // Create static border gradient that scales with container size
  const getStaticBorderBackground = () => {
    if (!enableSpotlight) return {};

    return {
      background: `
        radial-gradient(ellipse 150% 100% at 50% 30%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 100)}%, transparent) 0%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 70)}%, transparent) 50%,
          color-mix(in srgb, ${spotlightColor} ${Math.round(borderOpacity * 40)}%, transparent) 80%,
          transparent 100%)
      `
    };
  };

  return (
    <div
      data-slot="card"
      data-intensity={spotlightIntensity}
      className={cn(
        "bg-card text-card-foreground flex flex-col gap-4 rounded-xl border py-4 shadow-sm relative overflow-hidden",
        className
      )}
      style={{
        '--spotlight-color': spotlightColor,
        '--spotlight-bg-opacity': bgOpacity,
        '--spotlight-border-opacity': borderOpacity,
        ...style,
      } as React.CSSProperties}
    >
      {enableSpotlight && (
        <>
          {/* Static background spotlight effect */}
          <div
            className="absolute inset-0 pointer-events-none"
            style={getStaticSpotlightBackground()}
          />
          {/* Static border spotlight effect */}
          <div
            className="absolute inset-0 pointer-events-none rounded-xl"
            style={{
              ...getStaticBorderBackground(),
              mask: 'linear-gradient(white, white) content-box, linear-gradient(white, white)',
              maskComposite: 'xor',
              WebkitMask: 'linear-gradient(white, white) content-box, linear-gradient(white, white)',
              WebkitMaskComposite: 'xor',
              padding: '1px',
            }}
          />
        </>
      )}
      {children}
    </div>
  );
};

export { SpotlightCard };