"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSwipeToDismiss } from "@/hooks/use-swipe-to-dismiss";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import * as SheetPrimitive from "@radix-ui/react-dialog";
import { cn } from "@/lib/utils";

interface StackedMobileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

interface StackedMobileDialogContentProps extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content> {
  className?: string;
  children: React.ReactNode;
  fullHeight?: boolean;
  enableSwipeToDismiss?: boolean;
  onOpenChange?: (open: boolean) => void;
  swipeTopZoneHeight?: number;
}

const StackedMobileDialogContext = React.createContext<{
  onOpenChange: (open: boolean) => void;
}>({
  onOpenChange: () => {},
});

// Global context to track when a stacked modal is open
const StackedModalContext = React.createContext<{
  isStackedModalOpen: boolean;
  setIsStackedModalOpen: (open: boolean) => void;
}>({
  isStackedModalOpen: false,
  setIsStackedModalOpen: () => {},
});

export const useStackedModalContext = () => React.useContext(StackedModalContext);

export const StackedModalProvider = ({ children }: { children: React.ReactNode }) => {
  const [isStackedModalOpen, setIsStackedModalOpen] = React.useState(false);

  return (
    <StackedModalContext.Provider value={{ isStackedModalOpen, setIsStackedModalOpen }}>
      {children}
    </StackedModalContext.Provider>
  );
};

const StackedMobileDialog = ({ open, onOpenChange, children }: StackedMobileDialogProps) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { setIsStackedModalOpen } = useStackedModalContext();

  // Track stacked modal state
  React.useEffect(() => {
    setIsStackedModalOpen(open);
    return () => setIsStackedModalOpen(false);
  }, [open, setIsStackedModalOpen]);

  // Prevent body scroll when modal is open on mobile
  React.useEffect(() => {
    if (!isDesktop && open) {
      // Store the original overflow value from the style attribute, not computed style
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        // Restore the original overflow value, or remove the style if it wasn't set
        if (originalOverflow) {
          document.body.style.overflow = originalOverflow;
        } else {
          document.body.style.removeProperty('overflow');
        }
      };
    }
  }, [open, isDesktop]);

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[640px]">
          {children}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <StackedMobileDialogContext.Provider value={{ onOpenChange }}>
      <SheetPrimitive.Root open={open} onOpenChange={onOpenChange}>
        {children}
      </SheetPrimitive.Root>
    </StackedMobileDialogContext.Provider>
  );
};

const StackedMobileDialogContent = React.forwardRef<HTMLDivElement, StackedMobileDialogContentProps>(({
  className,
  children,
  fullHeight = false,
  enableSwipeToDismiss = true,
  onOpenChange: propOnOpenChange,
  swipeTopZoneHeight = 80,
  ...props
}, externalRef) => {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const context = React.useContext(StackedMobileDialogContext);

  // Use prop onOpenChange if provided, otherwise use context
  const onOpenChange = propOnOpenChange || context.onOpenChange;

  // Swipe-to-dismiss functionality for mobile
  const { containerRef: swipeToDismissRef, dragOffset, isDragging } = useSwipeToDismiss({
    onDismiss: () => onOpenChange?.(false),
    threshold: 100,
    enabled: enableSwipeToDismiss && !isDesktop,
    topZoneHeight: swipeTopZoneHeight,
  });

  // Combine refs
  const combinedRef = React.useCallback((node: HTMLDivElement | null) => {
    if (swipeToDismissRef) {
      swipeToDismissRef.current = node;
    }
    if (externalRef) {
      if (typeof externalRef === 'function') {
        externalRef(node);
      } else {
        externalRef.current = node;
      }
    }
  }, [swipeToDismissRef, externalRef]);

  // Full height styles for mobile
  const fullHeightStyles = fullHeight ? {
    height: '100dvh',
    maxHeight: '100dvh',
    borderRadius: 0,
  } : {};

  if (isDesktop) {
    return null; // Content is handled by Dialog on desktop
  }

  return (
    <SheetPrimitive.Portal>
      {/* Higher z-index overlay for stacked modal */}
      <SheetPrimitive.Overlay
        className={cn(
          "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[80] bg-black/30"
        )}
      />
      <motion.div
        ref={combinedRef}
        initial={{ 
          scale: 0.95, 
          opacity: 0,
          y: 20
        }}
        animate={{
          scale: 1,
          opacity: isDragging ? Math.max(0.5, 1 - dragOffset / 200) : 1,
          y: dragOffset,
        }}
        exit={{ 
          scale: 0.95, 
          opacity: 0,
          y: 20
        }}
        transition={{
          type: "spring",
          damping: isDragging ? 50 : 30,
          stiffness: isDragging ? 300 : 400,
          duration: isDragging ? 0 : 0.2, // Faster animation for stacked modal
        }}
        className="fixed inset-x-0 bottom-0 z-[90]"
        style={{
          touchAction: enableSwipeToDismiss ? 'none' : 'auto',
        }}
      >
        <SheetPrimitive.Content
          className={cn(
            "bg-background flex flex-col gap-4 shadow-xl transition ease-in-out w-full h-auto border-t min-h-0 overflow-y-auto overflow-x-hidden safe-area-inset-bottom",
            fullHeight ? "rounded-none" : "rounded-t-lg max-h-[70dvh]",
            className
          )}
          style={fullHeightStyles}
          {...props}
        >
          {/* Drag indicator for swipe-to-dismiss - top zone */}
          {enableSwipeToDismiss && (
            <div className="flex justify-center pt-3 pb-2 px-4" data-swipe-zone="true">
              <div className="w-10 h-1.5 bg-muted-foreground/40 rounded-full" />
            </div>
          )}
          {children}
        </SheetPrimitive.Content>
      </motion.div>
    </SheetPrimitive.Portal>
  );
});

StackedMobileDialogContent.displayName = "StackedMobileDialogContent";

const StackedMobileDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
);
StackedMobileDialogHeader.displayName = "StackedMobileDialogHeader";

const StackedMobileDialogTitle = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>
>(({ className, ...props }, ref) => (
  <SheetPrimitive.Title
    ref={ref}
    className={cn("text-lg font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));
StackedMobileDialogTitle.displayName = SheetPrimitive.Title.displayName;

export {
  StackedMobileDialog,
  StackedMobileDialogContent,
  StackedMobileDialogHeader,
  StackedMobileDialogTitle,
};
