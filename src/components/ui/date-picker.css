/* Date Picker Styles */
.rdp {
  --rdp-cell-size: 40px;
  --rdp-accent-color: var(--primary);
  --rdp-background-color: var(--primary);
  --rdp-accent-color-dark: var(--primary);
  --rdp-background-color-dark: var(--primary);
  --rdp-outline: 2px solid var(--ring);
  --rdp-outline-selected: 2px solid var(--primary);
  margin: 1rem 0;
}

.rdp-day_selected,
.rdp-day_selected:focus-visible,
.rdp-day_selected:hover {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: var(--muted);
}

.rdp-button:focus-visible:not([disabled]) {
  outline: var(--rdp-outline);
  outline-offset: 2px;
}

.rdp-button[disabled] {
  opacity: 0.5;
}

.rdp-day_today {
  font-weight: bold;
  color: var(--primary);
}

.rdp-day_outside {
  opacity: 0.5;
}

/* Keyboard navigation focus styles */
.rdp-button:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  position: relative;
  z-index: 1;
}

/* Responsive styles */
@media (max-width: 640px) {
  .rdp {
    --rdp-cell-size: 36px;
    font-size: 14px;
  }
}

/* High contrast mode support */
@media (forced-colors: active) {
  .rdp-day_selected {
    outline: 2px solid CanvasText;
  }
}

/* Animation for date picker */
.date-picker-enter {
  opacity: 0;
  transform: scale(0.95);
}

.date-picker-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 150ms ease-out, transform 150ms ease-out;
}

.date-picker-exit {
  opacity: 1;
  transform: scale(1);
}

.date-picker-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 150ms ease-in, transform 150ms ease-in;
}

/* Accessibility improvements */
.rdp-nav_button {
  padding: 4px;
  border-radius: 4px;
}

.rdp-nav_button:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.rdp-caption_label {
  font-weight: 500;
  font-size: 1rem;
}

/* Input field styles */
.date-picker-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 150ms ease, box-shadow 150ms ease;
  background-color: var(--background);
  color: var(--foreground);
  height: 2.5rem;
  padding-right: 2rem; /* Space for the clear button */
}

.date-picker-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.date-picker-input:hover:not(:disabled):not(:focus) {
  border-color: var(--input-hover);
}

.date-picker-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Fix for date input appearance in different browsers */
.date-picker-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
}

.date-picker-input-error,
.date-picker-input.error {
  border-color: var(--destructive);
}

.date-picker-error {
  color: var(--destructive);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Improve spacing and layout */
.date-picker-input-container {
  margin-top: 0;
  width: 100%;
}
