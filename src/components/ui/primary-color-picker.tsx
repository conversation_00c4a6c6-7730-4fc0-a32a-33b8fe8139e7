"use client";

import { useState } from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { ALL_LIST_COLORS, COLORLESS_OPTION, LIST_COLORS } from "@/lib/list-colors";

interface PrimaryColorPickerProps {
  value: string | null;
  onChange: (color: string | null) => void;
  disabled?: boolean;
}

export function PrimaryColorPicker({ value, onChange, disabled = false }: PrimaryColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedColor = ALL_LIST_COLORS.find(color => color.value === value) || COLORLESS_OPTION;

  const handleColorSelect = (color: string | null) => {
    onChange(color);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          "flex items-center gap-3 p-3 rounded-lg border transition-colors",
          "hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        <div className="flex items-center gap-3">
          <div
            className={cn(
              "w-6 h-6 rounded-full border-2 border-white shadow-sm",
              selectedColor.value ? `bg-[${selectedColor.value}]` : "bg-muted"
            )}
            style={selectedColor.value ? { backgroundColor: selectedColor.value } : undefined}
          />
          <span className="text-sm font-medium">{selectedColor.name}</span>
        </div>
        <div className="ml-auto text-muted-foreground">
          {isOpen ? "▲" : "▼"}
        </div>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-background border rounded-lg shadow-lg z-50">
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-foreground">Choose Primary Color</h4>
            
            {/* Colorless option */}
            <button
              type="button"
              onClick={() => handleColorSelect(null)}
              className={cn(
                "flex items-center gap-3 w-full p-2 rounded-md transition-colors",
                "hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring",
                value === null && "bg-muted"
              )}
            >
              <div className="w-5 h-5 rounded-full bg-muted border-2 border-white shadow-sm" />
              <span className="text-sm">Colorless (Default)</span>
              {value === null && <Check className="w-4 h-4 ml-auto text-primary" />}
            </button>

            {/* Color grid */}
            <div className="grid grid-cols-4 gap-2">
              {LIST_COLORS.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => handleColorSelect(color.value)}
                  className={cn(
                    "relative w-12 h-12 rounded-lg border-2 border-white shadow-sm transition-transform",
                    "hover:scale-105 focus:outline-none focus:ring-2 focus:ring-ring",
                    value === color.value && "ring-2 ring-primary ring-offset-2"
                  )}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                >
                  {value === color.value && (
                    <Check className="w-4 h-4 text-white absolute inset-0 m-auto drop-shadow-sm" />
                  )}
                </button>
              ))}
            </div>

            <p className="text-xs text-muted-foreground">
              This color will be used for buttons, links, and accents throughout the app.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
