"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Bold, Italic, List, Code } from "lucide-react";
import { cn } from "@/lib/utils";

export interface MarkdownToolbarProps {
  onFormat: (format: string, prefix: string, suffix?: string) => void;
  showMarkdown: boolean;
  onToggleMarkdown: () => void;
  className?: string;
}

export function MarkdownToolbar({ onFormat, showMarkdown, onToggleMarkdown, className }: MarkdownToolbarProps) {
  const toolbarButtons = [
    {
      icon: Bold,
      label: "Bold",
      format: "bold",
      prefix: "**",
      suffix: "**",
    },
    {
      icon: Italic,
      label: "Italic",
      format: "italic",
      prefix: "*",
      suffix: "*",
    },
    {
      icon: List,
      label: "Bullet List",
      format: "bullet",
      prefix: "- ",
    },
    // Numbered list temporarily hidden due to conversion issues
    // {
    //   icon: ListOrdered,
    //   label: "Numbered List",
    //   format: "numbered",
    //   prefix: "1. ",
    // },
  ];

  return (
    <div
      data-toolbar="true"
      className={cn(
        "flex items-center gap-1 p-2 border border-input bg-background rounded-md shadow-sm",
        className
      )}
    >
      {/* Formatting buttons */}
      {toolbarButtons.map((button) => {
        const Icon = button.icon;
        return (
          <Button
            key={button.format}
            variant="ghost"
            size="sm"
            className="h-7 w-7 p-0 hover:bg-muted"
            onClick={() => onFormat(button.format, button.prefix, button.suffix)}
            onMouseDown={(e) => e.preventDefault()} // Prevent textarea from losing focus
            type="button"
            aria-label={button.label}
            data-toolbar="true"
          >
            <Icon className="h-3.5 w-3.5" />
          </Button>
        );
      })}

      {/* Separator */}
      <div className="w-px h-4 bg-border mx-1" />

      {/* Markdown toggle button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-7 w-7 p-0 hover:bg-muted",
          showMarkdown && "bg-muted"
        )}
        onClick={onToggleMarkdown}
        onMouseDown={(e) => e.preventDefault()}
        type="button"
        aria-label={showMarkdown ? "Hide markdown syntax" : "Show markdown syntax"}
        data-toolbar="true"
      >
        <Code className="h-3.5 w-3.5" />
      </Button>
    </div>
  );
}
