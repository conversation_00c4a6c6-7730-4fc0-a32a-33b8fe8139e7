"use client";

import * as React from "react";
import { Clock } from "lucide-react";
import { cn } from "@/lib/utils";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MobileTimePicker } from "@/components/ui/mobile-time-picker";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Button } from "@/components/ui/button";

export interface TimePickerProps {
  value?: string; // HH:MM format
  onChange: (value: string) => void; // Used for immediate updates (mobile OK or legacy)
  className?: string;
  disabled?: boolean;
  use12Hour?: boolean;
  // Optional external modal state control for mobile
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  // Desktop: when true, show Cancel/OK and only commit on OK
  showActions?: boolean;
  onConfirm?: (value: string) => void;
  onCancel?: () => void;
}

export function TimePicker({
  value,
  onChange,
  className,
  disabled = false,
  use12Hour = true,
  open,
  onOpenChange,
  showActions = false,
  onConfirm,
  onCancel,
}: TimePickerProps) {
  const isMobile = useMediaQuery("(max-width: 767px)");

  // Parse the current time value with improved logic
  const parseTime = React.useCallback((timeStr?: string) => {
    if (!timeStr) return { hours: "", minutes: "", period: "AM" };

    const [hoursStr, minutesStr] = timeStr.split(':');
    const hourNum = parseInt(hoursStr, 10);
    const minuteNum = parseInt(minutesStr, 10);

    // Validate input
    if (isNaN(hourNum) || isNaN(minuteNum) || hourNum < 0 || hourNum > 23 || minuteNum < 0 || minuteNum > 59) {
      return { hours: "", minutes: "", period: "AM" };
    }

    if (use12Hour) {
      const period = hourNum >= 12 ? "PM" : "AM";
      let displayHour = hourNum;

      // Convert 24-hour to 12-hour format
      if (hourNum === 0) {
        displayHour = 12; // Midnight -> 12 AM
      } else if (hourNum > 12) {
        displayHour = hourNum - 12; // 13-23 -> 1-11 PM
      }
      // hourNum 1-11 stays the same, hourNum 12 stays 12

      return {
        hours: displayHour.toString(),
        minutes: minuteNum.toString().padStart(2, '0'),
        period
      };
    }

    return {
      hours: hourNum.toString().padStart(2, '0'),
      minutes: minuteNum.toString().padStart(2, '0'),
      period: "AM"
    };
  }, [use12Hour]);

  const { hours, minutes, period } = parseTime(value);

  // Temp state for desktop when actions are shown
  const [tempHours, setTempHours] = React.useState<string>(hours);
  const [tempMinutes, setTempMinutes] = React.useState<string>(minutes);
  const [tempPeriod, setTempPeriod] = React.useState<string>(period);

  // Keep temp values in sync with external value when not actively editing
  React.useEffect(() => {
    setTempHours(hours);
    setTempMinutes(minutes);
    setTempPeriod(period);
  }, [hours, minutes, period]);

  // Generate hour options
  const hourOptions = React.useMemo(() => {
    if (use12Hour) {
      return Array.from({ length: 12 }, (_, i) => {
        const hour = i + 1;
        return {
          value: hour.toString(),
          label: hour.toString()
        };
      });
    } else {
      return Array.from({ length: 24 }, (_, i) => {
        const hour = i.toString().padStart(2, '0');
        return {
          value: hour,
          label: hour
        };
      });
    }
  }, [use12Hour]);

  // Generate minute options (5-minute increments)
  const minuteOptions = React.useMemo(() => {
    return Array.from({ length: 12 }, (_, i) => {
      const minute = (i * 5).toString().padStart(2, '0');
      return {
        value: minute,
        label: minute
      };
    });
  }, []);

  // Convert 12-hour format to 24-hour format
  const convertTo24Hour = React.useCallback((hour12: number, period: string): number => {
    if (hour12 === 12) {
      return period === "AM" ? 0 : 12;
    }
    return period === "PM" ? hour12 + 12 : hour12;
  }, []);

  // Handle time changes with improved logic
  const handleHourChange = React.useCallback((newHour: string) => {
    const hourNum = parseInt(newHour, 10);
    if (isNaN(hourNum)) return;

    if (showActions) {
      setTempHours(newHour);
      return;
    }

    let hour24 = hourNum;
    if (use12Hour) {
      hour24 = convertTo24Hour(hourNum, period);
    }

    const timeString = `${hour24.toString().padStart(2, '0')}:${minutes}`;
    onChange(timeString);
  }, [minutes, period, use12Hour, onChange, convertTo24Hour, showActions]);

  const handleMinuteChange = React.useCallback((newMinute: string) => {
    const minuteNum = parseInt(newMinute, 10);
    if (isNaN(minuteNum)) return;

    if (showActions) {
      setTempMinutes(newMinute);
      return;
    }

    const currentTime = parseTime(value);
    const hourNum = parseInt(currentTime.hours, 10);
    if (isNaN(hourNum)) return;

    let hour24 = hourNum;
    if (use12Hour) {
      hour24 = convertTo24Hour(hourNum, currentTime.period);
    }

    const timeString = `${hour24.toString().padStart(2, '0')}:${newMinute}`;
    onChange(timeString);
  }, [value, parseTime, use12Hour, onChange, convertTo24Hour, showActions]);

  const handlePeriodChange = React.useCallback((newPeriod: string) => {
    if (showActions) {
      setTempPeriod(newPeriod);
      return;
    }

    const currentTime = parseTime(value);
    const hourNum = parseInt(currentTime.hours, 10);
    if (isNaN(hourNum)) return;

    // Convert the current 12-hour time to 24-hour with the new period
    const hour24 = convertTo24Hour(hourNum, newPeriod);

    const timeString = `${hour24.toString().padStart(2, '0')}:${minutes}`;
    onChange(timeString);
  }, [value, parseTime, minutes, onChange, convertTo24Hour, showActions]);

  const handleConfirmClick = React.useCallback(() => {
    if (!showActions) return;
    if (!tempHours || !tempMinutes) return;
    const hourNum = parseInt(tempHours, 10);
    if (isNaN(hourNum)) return;
    const minuteNum = parseInt(tempMinutes, 10);
    if (isNaN(minuteNum)) return;

    let hour24 = hourNum;
    if (use12Hour) {
      hour24 = convertTo24Hour(hourNum, tempPeriod);
    }

    const timeString = `${hour24.toString().padStart(2, '0')}:${tempMinutes}`;
    if (onConfirm) {
      onConfirm(timeString);
    } else {
      onChange(timeString);
    }
  }, [showActions, tempHours, tempMinutes, tempPeriod, use12Hour, convertTo24Hour, onConfirm, onChange]);

  const handleCancelClick = React.useCallback(() => {
    // Reset to current parsed values
    setTempHours(hours);
    setTempMinutes(minutes);
    setTempPeriod(period);
    onCancel?.();
  }, [hours, minutes, period, onCancel]);

  const canConfirm = React.useMemo(() => {
    return Boolean(tempHours) && Boolean(tempMinutes) && (!use12Hour || tempPeriod === "AM" || tempPeriod === "PM");
  }, [tempHours, tempMinutes, tempPeriod, use12Hour]);

  // Use mobile time picker on mobile devices (after all hooks are called)
  if (isMobile) {
    return (
      <MobileTimePicker
        value={value}
        onChange={onChange}
        className={className}
        disabled={disabled}
        use12Hour={use12Hour}
        open={open}
        onOpenChange={onOpenChange}
      />
    );
  }

  return (
    <div className={cn(showActions ? "flex flex-col gap-2" : "flex items-center gap-2", className)}>
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-muted-foreground" />

        {/* Hour Select */}
        <Select
          value={showActions ? tempHours : hours}
          onValueChange={handleHourChange}
          disabled={disabled}
        >
          <SelectTrigger
            className="w-16 h-8 text-sm focus:ring-2 focus:ring-ring focus:ring-offset-2 px-2"
            aria-label="Select hour"
          >
            <SelectValue placeholder="--" />
          </SelectTrigger>
          <SelectContent
            className="max-h-[200px] overflow-y-auto z-[100]"
            position="popper"
            sideOffset={4}
          >
            {hourOptions.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                className="focus:bg-accent focus:text-accent-foreground"
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <span className="text-muted-foreground">:</span>

        {/* Minute Select */}
        <Select
          value={showActions ? tempMinutes : minutes}
          onValueChange={handleMinuteChange}
          disabled={disabled}
        >
          <SelectTrigger
            className="w-16 h-8 text-sm focus:ring-2 focus:ring-ring focus:ring-offset-2 px-2"
            aria-label="Select minutes"
          >
            <SelectValue placeholder="--" />
          </SelectTrigger>
          <SelectContent
            className="max-h-[200px] overflow-y-auto z-[100]"
            position="popper"
            sideOffset={4}
          >
            {minuteOptions.map((option) => (
              <SelectItem
                key={option.value}
                value={option.value}
                className="focus:bg-accent focus:text-accent-foreground"
              >
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* AM/PM Select for 12-hour format */}
        {use12Hour && (
          <Select
            value={showActions ? tempPeriod : period}
            onValueChange={handlePeriodChange}
            disabled={disabled}
          >
            <SelectTrigger
              className="w-16 h-8 text-sm focus:ring-2 focus:ring-ring focus:ring-offset-2 px-2"
              aria-label="Select AM or PM"
            >
              <SelectValue />
            </SelectTrigger>
            <SelectContent
              className="max-h-[200px] overflow-y-auto z-[100]"
              position="popper"
              sideOffset={4}
            >
              <SelectItem
                value="AM"
                className="focus:bg-accent focus:text-accent-foreground"
              >
                AM
              </SelectItem>
              <SelectItem
                value="PM"
                className="focus:bg-accent focus:text-accent-foreground"
              >
                PM
              </SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>

      {showActions && (
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={handleCancelClick}>
            Cancel
          </Button>
          <Button variant="ghost" size="sm" onClick={handleConfirmClick} disabled={!canConfirm}>
            OK
          </Button>
        </div>
      )}
    </div>
  );
}
