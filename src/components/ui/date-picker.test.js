// Manual test script for the DatePicker component
// Run this in the browser console to test the DatePicker component

function testDatePicker() {
  console.log('Testing DatePicker component...');

  // Test inline date editing specifically
  console.log('Testing inline date editing in task items...');

  // Look for task items with date editing capability
  const taskItems = document.querySelectorAll('[data-testid="task-item"], .task-item, [class*="task"]');
  console.log(`Found ${taskItems.length} potential task items`);

  // Look for date display elements that can be clicked to edit
  const dateElements = document.querySelectorAll('[class*="calendar"], [aria-label*="date"], [class*="date"]');
  console.log(`Found ${dateElements.length} potential date elements`);

  // Test 1: Check if the DatePicker component renders correctly
  const datePickerButtons = document.querySelectorAll('[aria-haspopup="dialog"]');
  console.log(`Found ${datePickerButtons.length} DatePicker buttons`);

  if (datePickerButtons.length === 0) {
    console.error('No DatePicker buttons found. Make sure the DatePicker component is rendered on the page.');
    return;
  }

  // Test 2: Check if the DatePicker opens when clicked
  console.log('Clicking the first DatePicker button...');
  datePickerButtons[0].click();

  // Wait for the popover to open
  setTimeout(() => {
    const popover = document.querySelector('[role="dialog"][aria-label="Date picker calendar"]');

    if (!popover) {
      console.error('DatePicker popover did not open.');
      return;
    }

    console.log('DatePicker popover opened successfully.');

    // Test 3: Check if the calendar is rendered correctly
    const calendar = popover.querySelector('.rdp');

    if (!calendar) {
      console.error('Calendar not found in the DatePicker popover.');
      return;
    }

    console.log('Calendar rendered successfully.');

    // Test 4: Check if days are clickable
    const dayButtons = calendar.querySelectorAll('.rdp-day');

    if (dayButtons.length === 0) {
      console.error('No day buttons found in the calendar.');
      return;
    }

    console.log(`Found ${dayButtons.length} day buttons.`);

    // Test 5: Check if the Today button works
    const todayButton = popover.querySelector('button:contains("Today")');

    if (!todayButton) {
      console.log('Today button not found in popover.');
    } else {
      console.log('Today button found. Clicking it...');
      todayButton.click();

      // Wait for the popover to close and check for date pill with X button
      setTimeout(() => {
        const popoverAfterToday = document.querySelector('[role="dialog"][aria-label="Date picker calendar"]');

        if (popoverAfterToday) {
          console.log('Popover did not close after clicking the Today button.');
        } else {
          console.log('Popover closed successfully after clicking the Today button.');

          // Test 6: Check if date pill with X button appears
          const datePill = document.querySelector('[role="button"][aria-label*="Selected date"]');

          if (!datePill) {
            console.log('Date pill not found. This might be expected if no date was selected.');
          } else {
            console.log('Date pill found. Checking for X button...');

            const clearButton = datePill.querySelector('button[aria-label="Clear date"]');

            if (!clearButton) {
              console.error('Clear button (X) not found in date pill.');
            } else {
              console.log('Clear button (X) found in date pill. Clicking it...');
              clearButton.click();

              // Check if date pill disappears
              setTimeout(() => {
                const datePillAfterClear = document.querySelector('[role="button"][aria-label*="Selected date"]');

                if (datePillAfterClear) {
                  console.error('Date pill did not disappear after clicking clear button.');
                } else {
                  console.log('Date pill cleared successfully.');
                }
              }, 100);
            }
          }
        }

        console.log('All tests completed.');
      }, 100);
    }
  }, 100);
}

// Test specifically for inline date editing in task items
function testInlineDateEditing() {
  console.log('Testing inline date editing functionality...');

  // Look for task items
  const taskCards = document.querySelectorAll('[class*="border"]');
  console.log(`Found ${taskCards.length} potential task cards`);

  // Look for date elements that might be clickable for editing
  const dateDisplays = Array.from(document.querySelectorAll('*')).filter(el =>
    el.textContent && (
      el.textContent.includes('Add due date') ||
      el.textContent.match(/\d{1,2}\/\d{1,2}\/\d{4}/) ||
      el.textContent.match(/\w+ \d{1,2}, \d{4}/)
    )
  );

  console.log(`Found ${dateDisplays.length} potential date displays`);

  if (dateDisplays.length > 0) {
    console.log('Attempting to click on first date display to test inline editing...');
    const firstDateDisplay = dateDisplays[0];
    firstDateDisplay.click();

    // Wait a moment and check if date picker appeared
    setTimeout(() => {
      const activeDatePicker = document.querySelector('[role="dialog"]');
      if (activeDatePicker) {
        console.log('✅ Date picker opened successfully for inline editing');

        // Test clicking within the calendar
        const calendarDays = activeDatePicker.querySelectorAll('.rdp-day');
        if (calendarDays.length > 0) {
          console.log(`Found ${calendarDays.length} calendar days. Testing click on calendar...`);

          // Click on a day and see if picker stays open
          const firstDay = calendarDays[0];
          firstDay.click();

          setTimeout(() => {
            const pickerStillOpen = document.querySelector('[role="dialog"]');
            if (!pickerStillOpen) {
              console.log('✅ Date picker closed after date selection (expected behavior)');
            } else {
              console.log('⚠️ Date picker remained open after date selection');
            }
          }, 100);
        }
      } else {
        console.log('❌ Date picker did not open for inline editing');
      }
    }, 200);
  }
}

// Run the test
// testDatePicker();
// testInlineDateEditing();

// Instructions:
// 1. Open the browser console
// 2. Navigate to a page with the DatePicker component (e.g., /tasks)
// 3. Copy and paste this script into the console
// 4. Run testDatePicker() for general date picker testing
// 5. Run testInlineDateEditing() for testing the inline editing functionality
// 6. Check the console for test results
