"use client";

import { cn } from "@/lib/utils";
import { Progress } from "./progress";

interface UsageProgressProps {
  label: string;
  current: number;
  limit: number;
  className?: string;
}

export function UsageProgress({ label, current, limit, className }: UsageProgressProps) {
  const percentage = Math.round((current / limit) * 100);
  const isNearLimit = percentage >= 80;
  const isAtLimit = percentage >= 100;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{label}</span>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {current}/{limit}
          </span>
          <span className={cn(
            "text-xs font-medium px-2 py-1 rounded-full",
            isAtLimit 
              ? "bg-destructive/10 text-destructive" 
              : isNearLimit 
                ? "bg-yellow-500/10 text-yellow-600 dark:text-yellow-400"
                : "bg-muted text-muted-foreground"
          )}>
            {percentage}%
          </span>
        </div>
      </div>
      
      <Progress 
        value={Math.min(percentage, 100)} 
        className={cn(
          "h-2",
          isAtLimit && "[&>div]:bg-destructive",
          isNearLimit && !isAtLimit && "[&>div]:bg-yellow-500"
        )}
      />
      
      {isAtLimit && (
        <p className="text-xs text-destructive">
          You've reached your limit. Delete some old {label.toLowerCase()} or upgrade to create more.
        </p>
      )}
      {isNearLimit && !isAtLimit && (
        <p className="text-xs text-yellow-600 dark:text-yellow-400">
          You're approaching your limit for {label.toLowerCase()}.
        </p>
      )}
    </div>
  );
}
