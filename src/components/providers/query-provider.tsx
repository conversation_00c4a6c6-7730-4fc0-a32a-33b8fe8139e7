"use client";

import { useEffect } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { queryClient } from '@/lib/query-client';
import { setupCachePersistence } from '@/lib/cache-persistence';

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  // Setup cache persistence on mount
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializePersistence = async () => {
      try {
        cleanup = await setupCachePersistence(queryClient);
      } catch (error) {
        console.warn('Failed to initialize cache persistence:', error);
      }
    };

    initializePersistence();

    // Cleanup on unmount
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
