"use client";

import { useState, useEffect } from "react";
import { Save } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface FloatingSaveButtonProps {
  hasChanges: boolean;
  isLoading: boolean;
  onClick: () => void;
}

export function FloatingSaveButton({
  hasChanges,
  isLoading,
  onClick,
}: FloatingSaveButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={cn(
        "fixed bottom-20 z-40 transition-all duration-300",
        "md:bottom-4",
        // Position relative to container boundaries
        "right-4", // Mobile: 16px from right edge
        "md:right-[max(6rem,calc((100vw-896px)/2+6rem))]", // Desktop: 96px from container right edge (to account for mascot)
        isHovered ? "scale-110" : "scale-100",
        !hasChanges && "opacity-70"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Button
        size="icon"
        className={cn(
          "h-12 w-12 rounded-full shadow-md",
          hasChanges ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
        )}
        disabled={!hasChanges || isLoading}
        onClick={onClick}
      >
        {isLoading ? (
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
        ) : (
          <Save className="h-5 w-5" />
        )}
        <span className="sr-only">Save changes</span>
      </Button>
    </div>
  );
}
