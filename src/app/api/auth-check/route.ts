import { NextRequest, NextResponse } from "next/server";
import { stackServerApp } from "@/stack";

/**
 * This API route checks the Stack Auth configuration and returns the status
 */
export async function GET(request: NextRequest) {
  try {
    // Check if we can get the user from Stack Auth
    const user = await stackServerApp.getUser();
    
    // Return the authentication status
    return NextResponse.json({
      authenticated: !!user,
      user: user ? {
        id: user.id,
        email: user.primaryEmail,
        name: user.displayName,
      } : null,
      stack: {
        projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
        // Don't include the full keys for security reasons
        clientKeyConfigured: !!process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY,
        serverKeyConfigured: !!process.env.STACK_SECRET_SERVER_KEY,
      },
      cookies: {
        // Get all cookie names (don't include values for security)
        names: request.cookies.getAll().map(cookie => cookie.name),
      }
    });
  } catch (error) {
    // Return the error
    return NextResponse.json({
      authenticated: false,
      error: error instanceof Error ? error.message : String(error),
      stack: {
        projectId: process.env.NEXT_PUBLIC_STACK_PROJECT_ID,
        clientKeyConfigured: !!process.env.NEXT_PUBLIC_STACK_PUBLISHABLE_CLIENT_KEY,
        serverKeyConfigured: !!process.env.STACK_SECRET_SERVER_KEY,
      },
    }, { status: 500 });
  }
}
