import { NextRequest, NextResponse } from "next/server";
import { stackServerApp } from "@/stack";
import { getUserSettings, updateUserSettings } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const user = await stackServerApp.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const settings = await getUserSettings(user.id);

    if (!settings) {
      return NextResponse.json(
        { error: "Settings not found" },
        { status: 404 }
      );
    }

    // Return with no-cache headers to ensure we always get fresh data
    const response = NextResponse.json(settings);
    response.headers.set('Cache-Control', 'no-store, max-age=0');
    return response;
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await stackServerApp.getUser();

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const data = await request.json();

    const updatedSettings = await updateUserSettings(user.id, {
      theme: data.theme,
      notifications_enabled: data.notifications_enabled,
      week_starts_on: data.week_starts_on,
      mascot: data.mascot,
      primary_color: data.primary_color,
    });

    if (!updatedSettings) {
      return NextResponse.json(
        { error: "Failed to update settings" },
        { status: 500 }
      );
    }

    // Return with no-cache headers to ensure we always get fresh data
    const response = NextResponse.json({ success: true, data: updatedSettings });
    response.headers.set('Cache-Control', 'no-store, max-age=0');
    return response;
  } catch (error) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
