import { NextRequest, NextResponse } from "next/server";

/**
 * This is a proxy API route to bypass CORS restrictions when connecting to Stack Auth API
 * It forwards requests to the Stack Auth API and returns the response
 */
export async function GET(request: NextRequest) {
  try {
    // Get the target URL from the query parameters
    let url = request.nextUrl.searchParams.get("url");

    if (!url) {
      return NextResponse.json({ error: "Missing url parameter" }, { status: 400 });
    }

    // Get the path parameter if present
    const path = request.nextUrl.searchParams.get("path");
    if (path) {
      // Append the path to the URL
      url = `${url}${path}`;
    }

    // Get additional query parameters
    const searchParams = new URLSearchParams();
    for (const [key, value] of request.nextUrl.searchParams.entries()) {
      // Skip the url and path parameters
      if (key !== "url" && key !== "path") {
        searchParams.append(key, value);
      }
    }

    // Append query parameters if present
    const queryString = searchParams.toString();
    if (queryString) {
      url = `${url}?${queryString}`;
    }

    console.log("Auth proxy forwarding to:", url);

    // Forward the request to the Stack Auth API
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        // Forward authorization headers if present
        ...(request.headers.get("Authorization")
          ? { "Authorization": request.headers.get("Authorization")! }
          : {}),
      },
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error("Auth proxy error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * This handles POST requests to the proxy
 */
export async function POST(request: NextRequest) {
  try {
    // Get the target URL and body from the request
    let url = request.nextUrl.searchParams.get("url");
    let body;

    try {
      body = await request.json();
    } catch (e) {
      // If the body is not JSON, use an empty object
      body = {};
    }

    if (!url) {
      return NextResponse.json({ error: "Missing url parameter" }, { status: 400 });
    }

    // Get the path parameter if present
    const path = request.nextUrl.searchParams.get("path");
    if (path) {
      // Append the path to the URL
      url = `${url}${path}`;
    }

    // Get additional query parameters
    const searchParams = new URLSearchParams();
    for (const [key, value] of request.nextUrl.searchParams.entries()) {
      // Skip the url and path parameters
      if (key !== "url" && key !== "path") {
        searchParams.append(key, value);
      }
    }

    // Append query parameters if present
    const queryString = searchParams.toString();
    if (queryString) {
      url = `${url}?${queryString}`;
    }

    console.log("Auth proxy forwarding POST to:", url);

    // Forward the request to the Stack Auth API
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Forward authorization headers if present
        ...(request.headers.get("Authorization")
          ? { "Authorization": request.headers.get("Authorization")! }
          : {}),
      },
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error("Auth proxy error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
