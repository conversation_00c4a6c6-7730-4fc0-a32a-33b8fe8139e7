import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, StackTheme } from "@stackframe/stack";
import { stackServerApp } from "../stack";
import { Nunito_Sans } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { QueryProvider } from "@/components/providers/query-provider";
import { StackedModalProvider } from "@/components/ui/stacked-mobile-dialog";
import { PWAInstaller } from "@/components/pwa-installer";
import "./globals.css";

const nunitoSans = Nunito_Sans({
  variable: "--font-nunito-sans",
  subsets: ["latin"],
  weight: ["200", "300", "400", "500", "600", "700"],
  display: "swap",
});

// Keep a fallback monospace font for code elements
const systemMono = {
  variable: "--font-mono",
};

export const metadata: Metadata = {
  title: "NeoTask",
  description: "A Next.js task management app with Neon DB and Stack Auth",
  icons: {
    icon: "/NeoTask Icon Black 192.png",
    apple: "/NeoTask Icon Black 192.png",
  },
  manifest: "/manifest.json",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover, interactive-widget=overlays-content" />
        <meta name="theme-color" content="#000000" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="NeoTask" />
        <link rel="apple-touch-icon" href="/NeoTask Icon Black 192.png" />
      </head>
      <body
        className={`${nunitoSans.variable} ${systemMono.variable} antialiased`}
      >
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <QueryProvider>
            <StackProvider app={stackServerApp}>
              <StackTheme>
                <StackedModalProvider>
                  <PWAInstaller />
                  {children}
                </StackedModalProvider>
              </StackTheme>
            </StackProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
