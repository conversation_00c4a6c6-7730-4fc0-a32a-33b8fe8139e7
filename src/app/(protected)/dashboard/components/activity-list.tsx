"use client";

import { useState, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { TaskActivity } from "@/lib/db";
import { fetchRecentActivities } from "@/app/actions/activities";
import { ActivityItem } from "./activity-item";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { LoadingContainer } from "@/components/ui/loading-spinner";

const ITEMS_PER_PAGE = 5;

export function ActivityList() {
  const user = useUser();
  const [activities, setActivities] = useState<TaskActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (user) {
      loadActivities();
    }
  }, [user]);

  const loadActivities = async (reset = true) => {
    if (!user) return;
    
    if (reset) {
      setIsLoading(true);
      setOffset(0);
    } else {
      setIsLoadingMore(true);
    }
    
    try {
      const newOffset = reset ? 0 : offset;
      const newActivities = await fetchRecentActivities(user.id, ITEMS_PER_PAGE, newOffset);
      
      if (reset) {
        setActivities(newActivities);
      } else {
        setActivities([...activities, ...newActivities]);
      }
      
      setHasMore(newActivities.length === ITEMS_PER_PAGE);
      setOffset(newOffset + newActivities.length);
    } catch (error) {
      console.error("Error loading activities:", error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadActivities(false);
    }
  };

  if (isLoading) {
    return <LoadingContainer text="Loading activities..." />;
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No recent activity. Start creating tasks to see activity here.
      </div>
    );
  }

  return (
    <div className="space-y-1">
      <div className="divide-y">
        {activities.map((activity) => (
          <ActivityItem key={activity.id} activity={activity} />
        ))}
      </div>
      
      {hasMore && (
        <div className="pt-4 text-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleLoadMore}
            disabled={isLoadingMore}
          >
            {isLoadingMore ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More"
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
