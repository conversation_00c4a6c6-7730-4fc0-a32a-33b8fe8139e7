"use client";

import { format } from "date-fns";
import { CheckCircle2, PlusCircle } from "lucide-react";
import { TaskActivity } from "@/lib/db";
import Link from "next/link";

interface ActivityItemProps {
  activity: TaskActivity;
}

export function ActivityItem({ activity }: ActivityItemProps) {
  const isCreated = activity.activity_type === "created";
  const isCompleted = activity.activity_type === "completed";

  return (
    <div className="flex items-start gap-3 py-3">
      <div className="flex-shrink-0 mt-1">
        {isCreated && (
          <PlusCircle className="h-5 w-5 text-primary" />
        )}
        {isCompleted && (
          <CheckCircle2 className="h-5 w-5 text-green-500" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">
          {isCreated && "Created task: "}
          {isCompleted && "Completed task: "}
          <Link
            href="/tasks"
            className="hover:underline text-white"
          >
            {activity.task_title}
          </Link>
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          {format(new Date(activity.created_at), "MMM d, yyyy 'at' h:mm a")}
        </p>
      </div>
    </div>
  );
}
