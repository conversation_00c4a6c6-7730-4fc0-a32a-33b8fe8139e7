"use client";

import { useState, useMemo } from "react";
import { ActivityWithTaskTitle } from "@/lib/types";
import { fetchRecentActivities } from "@/app/actions/activities";
import { format } from "date-fns";
import { Check<PERSON>ircle2, Plus<PERSON>ircle, Loader2, Filter } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

type ActivityFilter = "all" | "created" | "completed";

interface ActivityFeedProps {
  userId: string;
  initialActivities: ActivityWithTaskTitle[];
}

export function ActivityFeed({ userId, initialActivities }: ActivityFeedProps) {
  const [activities, setActivities] = useState<ActivityWithTaskTitle[]>(initialActivities);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(initialActivities.length >= 5);
  const [activeFilter, setActiveFilter] = useState<ActivityFilter>("all");

  const handleLoadMore = async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);

    try {
      const newActivities = await fetchRecentActivities(userId, 5, activities.length);

      if (newActivities.length === 0 || newActivities.length < 5) {
        setHasMore(false);
      }

      // Cast the newActivities to the correct type
      setActivities([...activities, ...(newActivities as ActivityWithTaskTitle[])]);
    } catch (error) {
      console.error("Error loading more activities:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (value: string) => {
    if (value === "all" || value === "created" || value === "completed") {
      setActiveFilter(value);
    }
  };

  const filteredActivities = useMemo(() => {
    if (activeFilter === "all") {
      return activities;
    }
    return activities.filter(activity => activity.activity_type === activeFilter);
  }, [activities, activeFilter]);

  if (activities.length === 0) {
    return (
      <div className="text-sm text-muted-foreground">
        <p>No recent activity to display.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-start items-center">
        <ToggleGroup
          type="single"
          value={activeFilter}
          onValueChange={handleFilterChange}
          className="w-full min-h-[42px]"
        >
          <ToggleGroupItem
            value="all"
            aria-label="Show all activities"
            className="flex flex-col items-center pt-1.5 pb-1 px-2 h-auto flex-1"
          >
            <div className="flex-none">
              <Filter className="h-3.5 w-3.5 mb-0.5" />
            </div>
            <span className="text-[8px]">All Activities</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="created"
            aria-label="Show created tasks only"
            className="flex flex-col items-center pt-1.5 pb-1 px-2 h-auto flex-1"
          >
            <div className="flex-none">
              <PlusCircle className="h-3.5 w-3.5 mb-0.5" />
            </div>
            <span className="text-[8px]">Created Tasks</span>
          </ToggleGroupItem>
          <ToggleGroupItem
            value="completed"
            aria-label="Show completed tasks only"
            className="flex flex-col items-center pt-1.5 pb-1 px-2 h-auto flex-1"
          >
            <div className="flex-none">
              <CheckCircle2 className="h-3.5 w-3.5 mb-0.5" />
            </div>
            <span className="text-[8px]">Completed Tasks</span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {filteredActivities.length === 0 ? (
        <div className="text-sm text-muted-foreground py-4">
          <p>No {activeFilter !== "all" ? activeFilter : ""} activities to display.</p>
        </div>
      ) : (
        <div className="divide-y">
          {filteredActivities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 py-3">
              <div className="flex-shrink-0 mt-1">
                {activity.activity_type === "created" && (
                  <PlusCircle className="h-5 w-5 text-primary" />
                )}
                {activity.activity_type === "completed" && (
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">
                  {activity.activity_type === "created" && "Created task: "}
                  {activity.activity_type === "completed" && "Completed task: "}
                  <Link
                    href="/tasks"
                    className="hover:underline text-white truncate max-w-[200px] sm:max-w-[280px] md:max-w-[320px] inline-block"
                    title={activity.task_title}
                  >
                    {activity.task_title}
                  </Link>
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {format(new Date(activity.created_at), "MMM d, yyyy 'at' h:mm a")}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}

      {hasMore && activeFilter === "all" && (
        <div className="pt-2 text-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleLoadMore}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More"
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
