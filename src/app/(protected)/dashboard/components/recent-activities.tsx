import { getRecentTaskActivities } from "@/lib/db";
import { ActivityWithTaskTitle } from "@/lib/types";
import { ActivityFeed } from "./activity-feed";

interface RecentActivitiesProps {
  userId: string;
}

export async function RecentActivities({ userId }: RecentActivitiesProps) {
  const activities = await getRecentTaskActivities(userId, 5, 0);

  // Cast the activities to the correct type
  return <ActivityFeed userId={userId} initialActivities={activities as ActivityWithTaskTitle[]} />;
}
