import { TaskWithList } from "@/lib/types";
import { fetchUpcomingTasks } from "@/app/actions/tasks";
import Link from "next/link";
import { isToday, isTomorrow } from "date-fns";
import { CalendarClock, CalendarDays, CheckCircle2, AlertTriangle } from "lucide-react";
import { formatDate, getStartOfToday, getTomorrow, isPastDue } from "@/lib/date-utils";
import { Button } from "@/components/ui/button";

interface DueDateSummaryProps {
  userId: string;
}

interface TaskGroup {
  title: string;
  icon: React.ReactNode;
  tasks: TaskWithList[];
}

export async function DueDateSummary({ userId }: DueDateSummaryProps) {
  const tasks = await fetchUpcomingTasks(userId);

  if (tasks.length === 0) {
    return null; // Don't show the component if there are no upcoming tasks
  }

  // Group tasks by due date
  const today = getStartOfToday();
  const tomorrow = getTomorrow();

  const taskGroups: TaskGroup[] = [
    {
      title: "Past Due",
      icon: <AlertTriangle className="h-4 w-4 text-red-600" />,
      tasks: tasks.filter(task => task.due_date && isPastDue(task.due_date))
    },
    {
      title: "Today",
      icon: <CalendarDays className="h-4 w-4 text-red-500" />,
      tasks: tasks.filter(task => task.due_date && isToday(new Date(task.due_date)))
    },
    {
      title: "Tomorrow",
      icon: <CalendarDays className="h-4 w-4 text-amber-500" />,
      tasks: tasks.filter(task => task.due_date && isTomorrow(new Date(task.due_date)))
    },
    {
      title: "Upcoming",
      icon: <CalendarClock className="h-4 w-4 text-blue-500" />,
      tasks: tasks.filter(task => {
        if (!task.due_date) return false;
        const dueDate = new Date(task.due_date);
        return !isToday(dueDate) && !isTomorrow(dueDate) && !isPastDue(dueDate);
      })
    }
  ];

  // Filter out empty groups
  const nonEmptyGroups = taskGroups.filter(group => group.tasks.length > 0);

  if (nonEmptyGroups.length === 0) {
    return null; // Don't show the component if all groups are empty
  }

  return (
    <div className="space-y-4">
      {nonEmptyGroups.map((group) => (
        <div key={group.title} className="space-y-2">
          <div className="flex items-center gap-2">
            {group.icon}
            <h3 className="text-sm font-normal">{group.title}</h3>
          </div>
          <ul className="space-y-1">
            {group.tasks.map((task) => (
              <li key={task.id} className="text-sm">
                <Link
                  href="/tasks"
                  className="flex items-center gap-2 py-1 hover:underline"
                >
                  <div className="flex-shrink-0">
                    <div
                      className="h-2 w-2 rounded-full border border-muted-foreground/30"
                      style={{
                        backgroundColor: task.list_color || '#f8f9fa'
                      }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <span className="font-normal">{task.title}</span>
                    {(group.title === "Upcoming" || group.title === "Past Due") && task.due_date && (
                      <span className="ml-2 text-xs text-muted-foreground">
                        {formatDate(task.due_date, "MMM d")}
                      </span>
                    )}
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ))}

      <div className="pt-2 text-center">
        <Button
          variant="outline"
          size="sm"
          asChild
        >
          <Link href="/tasks">
            View all tasks
          </Link>
        </Button>
      </div>
    </div>
  );
}
