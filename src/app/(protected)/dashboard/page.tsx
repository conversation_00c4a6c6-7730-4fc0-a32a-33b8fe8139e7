import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { stackServerApp } from "@/stack";
import { getUserById } from "@/lib/db";
import { redirect } from "next/navigation";
import { RecentActivities } from "./components/recent-activities";
import { DueDateSummary } from "./components/due-date-summary";

export default async function DashboardPage() {
  const user = await stackServerApp.getUser({ or: "redirect" });

  if (!user) {
    redirect("/handler/sign-in");
  }

  // Get additional user data from our database
  const dbUser = await getUserById(user.id);

  return (
    <div className="px-2 md:px-4 py-3 space-y-6">
      <div className="grid gap-4">
        <Card className="gradient-border-muted">
          <CardHeader>
            <CardTitle>Upcoming Tasks</CardTitle>
            <CardDescription>Tasks with upcoming due dates</CardDescription>
          </CardHeader>
          <CardContent>
            <DueDateSummary userId={user.id} />
          </CardContent>
        </Card>

        <Card className="gradient-border-muted">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your recent task creations and completions</CardDescription>
          </CardHeader>
          <CardContent>
            <RecentActivities userId={user.id} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
