"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface CleanupConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  completedTasksCount: number;
}

export function CleanupConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  completedTasksCount,
}: CleanupConfirmationDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent aria-labelledby="delete-completed-tasks-title">
        <AlertDialogTitle id="delete-completed-tasks-title" className="sr-only">
          Delete Completed Tasks
        </AlertDialogTitle>
        <AlertDialogDescription>
          Are you sure you want to permanently delete all {completedTasksCount} completed task{completedTasksCount !== 1 ? 's' : ''}?
          <br />
          <br />
          This action cannot be undone and will completely remove these tasks from your account.
        </AlertDialogDescription>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            Delete All Completed Tasks
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
