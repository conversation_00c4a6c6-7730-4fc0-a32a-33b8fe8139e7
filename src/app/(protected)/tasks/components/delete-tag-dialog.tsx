"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { removeTag } from "@/app/actions/tags";
import { useUser } from "@stackframe/stack";
import { TagFilter } from "@/lib/types";

interface DeleteTagDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTagDeleted: () => void;
  tag: TagFilter | null;
}

export function DeleteTagDialog({
  open,
  onOpenChange,
  onTagDeleted,
  tag,
}: DeleteTagDialogProps) {
  const user = useUser({ or: "redirect" });
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (user && tag) {
      setIsDeleting(true);
      try {
        const success = await removeTag(tag.id, user.id);
        if (success) {
          onTagDeleted();
        }
      } catch (error) {
        console.error("Error deleting tag:", error);
      } finally {
        setIsDeleting(false);
      }
    }
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent aria-labelledby="delete-tag-title">
        <AlertDialogTitle id="delete-tag-title" className="sr-only">
          Delete Tag
        </AlertDialogTitle>
        <AlertDialogDescription>
          Are you sure you want to delete the tag "{tag?.name}"?
          <br />
          <br />
          This will remove it from all tasks that use this tag.
          <br />
          <br />
          This action cannot be undone.
        </AlertDialogDescription>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete Tag"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
