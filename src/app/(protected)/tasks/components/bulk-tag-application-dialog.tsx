"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { Tag } from "@/lib/db";
import { useTagsQuery, useCreateTagMutation } from "@/lib/queries";
import { useSpace } from "@/contexts/space-context";
import { searchTags } from "@/app/actions/tags";
import { Button } from "@/components/ui/button";
import { MobileDialog, MobileDialogContent, MobileDialogHeader, MobileDialogTitle } from "@/components/ui/mobile-dialog";
import { TagPicker } from "@/components/ui/tag-picker";
import { ChevronLeft } from "lucide-react";

interface BulkTagApplicationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (tagId: string) => Promise<void>;
  selectedTasksCount: number;
}

export function BulkTagApplicationDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedTasksCount,
}: BulkTagApplicationDialogProps) {
  const user = useUser();
  const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
  const [isApplying, setIsApplying] = useState(false);

  const { currentSpace } = useSpace();
  // TanStack Query hooks
  const { data: availableTags = [] } = useTagsQuery(currentSpace?.id || "", user?.id || "");
  const createTagMutation = useCreateTagMutation(user?.id || "", currentSpace?.id || "");

  const handleTagSelect = (tag: Tag) => {
    setSelectedTag(tag);
  };

  const handleTagRemove = (tag: Tag) => {
    if (selectedTag?.id === tag.id) {
      setSelectedTag(null);
    }
  };

  const handleTagCreate = async (name: string, color: string): Promise<Tag | null> => {
    if (!user?.id) return null;

    try {
      const newTag = await createTagMutation.mutateAsync({
        name,
        color,
        userId: user.id,
      });
      
      if (newTag) {
        setSelectedTag(newTag);
      }
      
      return newTag;
    } catch (error) {
      console.error("Error creating tag:", error);
      return null;
    }
  };

  const handleSearchTags = async (searchTerm: string): Promise<Tag[]> => {
    if (!user?.id) return [];
    
    try {
      return await searchTags(user.id, currentSpace?.id || "", searchTerm);
    } catch (error) {
      console.error("Error searching tags:", error);
      return [];
    }
  };

  const handleConfirm = async () => {
    if (!selectedTag) return;
    
    setIsApplying(true);
    try {
      await onConfirm(selectedTag.id);
    } catch (error) {
      console.error("Error applying tag:", error);
    } finally {
      setIsApplying(false);
      onOpenChange(false);
      setSelectedTag(null);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelectedTag(null);
  };

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent>
        <MobileDialogHeader>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="absolute left-4 top-4 h-8 w-8 p-0"
          >
            <ChevronLeft className="h-5 w-5" />
            <span className="sr-only">Cancel</span>
          </Button>
          <MobileDialogTitle>
            Apply Tag to {selectedTasksCount} Task{selectedTasksCount !== 1 ? 's' : ''}
          </MobileDialogTitle>
        </MobileDialogHeader>

        <div className="grid gap-4 px-4 pb-4 md:px-0">
          <div className="grid gap-2">
            <span className="text-sm font-medium">Select a tag to apply:</span>
            
            {/* Tag Picker */}
            <TagPicker
              selectedTags={selectedTag ? [selectedTag] : []}
              availableTags={availableTags}
              onTagSelect={handleTagSelect}
              onTagRemove={handleTagRemove}
              onTagCreate={handleTagCreate}
              onSearchTags={handleSearchTags}
              placeholder="Search or create a tag..."
              disabled={isApplying}
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isApplying}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!selectedTag || isApplying}
              className="flex-1"
            >
              {isApplying ? "Applying..." : `Apply to ${selectedTasksCount} Task${selectedTasksCount !== 1 ? 's' : ''}`}
            </Button>
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
