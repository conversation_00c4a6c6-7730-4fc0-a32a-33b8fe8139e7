"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { List } from "@/lib/db";
import { useListsBySpaceQuery, useTaskCountsQuery } from "@/lib/queries";
import { getContrastTextColor } from "@/lib/list-colors";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MobileDialog, MobileDialogContent, MobileDialogHeader, MobileDialogTitle } from "@/components/ui/mobile-dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ChevronDown, Check, ChevronLeft } from "lucide-react";

interface BulkMoveToListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (newListId: string) => Promise<void>;
  selectedTasksCount: number;
  currentListId: string;
  currentSpaceId: string;
}

export function BulkMoveToListDialog({
  open,
  onOpenChange,
  onConfirm,
  selectedTasksCount,
  currentListId,
  currentSpaceId,
}: BulkMoveToListDialogProps) {
  const user = useUser();
  const [selectedListId, setSelectedListId] = useState<string | null>(null);
  const [isMoving, setIsMoving] = useState(false);

  // TanStack Query hooks
  const { data: availableLists = [] } = useListsBySpaceQuery(user?.id || "", currentSpaceId);
  const { data: taskCounts = {} } = useTaskCountsQuery(user?.id || "");

  // Filter out current list
  const otherLists = availableLists.filter(list => list.id !== currentListId);

  const handleConfirm = async () => {
    if (!selectedListId) return;
    
    setIsMoving(true);
    try {
      await onConfirm(selectedListId);
    } catch (error) {
      console.error("Error moving tasks:", error);
    } finally {
      setIsMoving(false);
      onOpenChange(false);
      setSelectedListId(null);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelectedListId(null);
  };

  const selectedList = availableLists.find(list => list.id === selectedListId);

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent>
        <MobileDialogHeader>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="absolute left-4 top-4 h-8 w-8 p-0"
          >
            <ChevronLeft className="h-5 w-5" />
            <span className="sr-only">Cancel</span>
          </Button>
          <MobileDialogTitle>
            Move {selectedTasksCount} Task{selectedTasksCount !== 1 ? 's' : ''} to List
          </MobileDialogTitle>
        </MobileDialogHeader>

        <div className="grid gap-4 px-4 pb-4 md:px-0">
          <div className="grid gap-2">
            <span className="text-sm font-medium">Select destination list:</span>
            
            {/* List Selection Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between text-left font-normal"
                  disabled={isMoving}
                >
                  {selectedList ? selectedList.name : "Select a list"}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full max-h-[200px] overflow-y-auto">
                {otherLists.map((list) => {
                  const hasColor = list.color !== null;
                  const taskCount = taskCounts?.[list.id] || 0;

                  const getHoverStyles = () => {
                    if (!hasColor) return {};
                    
                    const textColor = getContrastTextColor(list.color!);
                    return {
                      '--hover-bg': list.color,
                      '--hover-color': textColor,
                    };
                  };

                  const getBadgeStyles = () => {
                    if (!hasColor) return {};
                    
                    const textColor = getContrastTextColor(list.color!);
                    return {
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: textColor,
                    };
                  };

                  return (
                    <DropdownMenuItem
                      key={list.id}
                      onClick={() => setSelectedListId(list.id)}
                      className="flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors [&:hover]:bg-[var(--hover-bg)] [&:hover]:text-[var(--hover-color)]"
                      style={getHoverStyles() as React.CSSProperties}
                    >
                      {list.id === selectedListId && (
                        <Check className="h-4 w-4 text-current" />
                      )}
                      <span className="truncate max-w-[120px] font-medium text-sm">
                        {list.name}
                      </span>
                      {taskCount > 0 && (
                        <Badge
                          className="text-xs border-transparent"
                          style={getBadgeStyles()}
                        >
                          {taskCount}
                        </Badge>
                      )}
                    </DropdownMenuItem>
                  );
                })}
                {otherLists.length === 0 && (
                  <DropdownMenuItem disabled>
                    No other lists available
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isMoving}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!selectedListId || isMoving}
              className="flex-1"
            >
              {isMoving ? "Moving..." : `Move ${selectedTasksCount} Task${selectedTasksCount !== 1 ? 's' : ''}`}
            </Button>
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
