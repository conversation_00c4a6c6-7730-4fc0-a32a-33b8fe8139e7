"use client";

import { useState, useEffect, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { useEditTagMutation } from "@/lib/queries";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ColorPicker } from "@/components/ui/color-picker";
import { Tag } from "@/lib/db";
import { TagFilter } from "@/lib/types";
import { ChevronLeft } from "lucide-react";
import { useSpace } from "@/contexts/space-context";

interface EditTagDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTagEdited: (updatedTag?: Tag) => void;
  tag: TagFilter | null;
}

export function EditTagDialog({
  open,
  onOpenChange,
  onTagEdited,
  tag,
}: EditTagDialogProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [color, setColor] = useState<string>("#3b82f6");

  // Refs for auto-save debouncing
  const nameTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const colorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // TanStack Query mutation
  const { currentSpace } = useSpace();
  const editTagMutation = useEditTagMutation(user?.id || "", currentSpace?.id || "");

  // Initialize form when tag changes
  useEffect(() => {
    if (tag) {
      setName(tag.name);
      setColor(tag.color);
    }
  }, [tag]);

  // Auto-save name changes
  const handleNameChange = (newName: string) => {
    setName(newName);

    // Clear existing timeout
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }

    // Only auto-save if we have a tag and user
    if (!tag || !user?.id) return;

    // Set new timeout for auto-save
    nameTimeoutRef.current = setTimeout(async () => {
      if (newName.trim() && newName.trim() !== tag.name) {
        try {
          const updatedTag = await editTagMutation.mutateAsync({
            tagId: tag.id,
            data: { name: newName.trim() }
          });
          if (updatedTag) {
            onTagEdited(updatedTag);
          }
        } catch (error) {
          console.error("Error updating tag name:", error);
          // Revert on error
          setName(tag.name);
        }
      }
    }, 1000); // 1 second debounce
  };

  // Auto-save color changes
  const handleColorChange = (newColor: string) => {
    setColor(newColor);

    // Clear existing timeout
    if (colorTimeoutRef.current) {
      clearTimeout(colorTimeoutRef.current);
    }

    // Only auto-save if we have a tag and user
    if (!tag || !user?.id) return;

    // Set new timeout for auto-save
    colorTimeoutRef.current = setTimeout(async () => {
      if (newColor !== tag.color) {
        try {
          const updatedTag = await editTagMutation.mutateAsync({
            tagId: tag.id,
            data: { color: newColor }
          });
          if (updatedTag) {
            onTagEdited(updatedTag);
          }
        } catch (error) {
          console.error("Error updating tag color:", error);
          // Revert on error
          setColor(tag.color);
        }
      }
    }, 500); // 0.5 second debounce for color
  };

  const handleClose = () => {
    // Clear any pending timeouts
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }
    if (colorTimeoutRef.current) {
      clearTimeout(colorTimeoutRef.current);
    }
    onOpenChange(false);
  };

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (nameTimeoutRef.current) {
        clearTimeout(nameTimeoutRef.current);
      }
      if (colorTimeoutRef.current) {
        clearTimeout(colorTimeoutRef.current);
      }
    };
  }, []);

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[640px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Edit Tag</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0 md:px-6 md:pt-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="grid gap-4 px-4 pb-4 md:px-6">
          <div className="grid gap-2">
            <Input
              id="name"
              type="search"
              value={name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="Tag name"
              autoComplete="off"
              spellCheck="false"
              maxLength={50}
            />
          </div>

          <div className="grid gap-2">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium">Color:</span>
              <ColorPicker
                value={color}
                onChange={handleColorChange}
              />
            </div>
          </div>
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
