"use client";

import { useState, useEffect, useCallback } from "react";
import { useUser } from "@stackframe/stack";
import { searchTags } from "@/app/actions/tags";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { InlineTagPicker } from "@/components/ui/inline-tag-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronDown, Check, ChevronLeft } from "lucide-react";
import type { Tag, Task, TaskSortOption } from "@/lib/db";
import {
  useTagsQuery,
  useAddTaskMutation,
  useCreateTagMutation,
  useCreatePicklistTagMutation,
  useListsBySpaceQuery,
  useTaskCountsQuery
} from "@/lib/queries";
import type { CreateTagData } from "@/lib/types";
import { getContrastTextColor } from "@/lib/list-colors";
import { StatusSquare } from "./status-square";
import { useUsageBanner } from "@/components/ui/usage-banner";
import { getUsageLimitMessage } from "@/lib/usage-messages";

interface AddTaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskAdded: (newTask?: Task) => void;
  listId: string | null;
  sortOption?: TaskSortOption;
  currentSpaceId: string;
  initialStatus?: 'not_started' | 'in_progress' | 'completed';
}

export function AddTaskDialog({
  open,
  onOpenChange,
  onTaskAdded,
  listId,
  sortOption = "position",
  currentSpaceId,
  initialStatus = 'not_started',
}: AddTaskDialogProps) {
  const user = useUser();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dueDate, setDueDate] = useState<Date | undefined | null>(undefined);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [selectedListId, setSelectedListId] = useState<string>(listId || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<'not_started' | 'in_progress' | 'completed'>(initialStatus);

  // TanStack Query hooks
  const { data: availableTags = [] } = useTagsQuery(currentSpaceId, user?.id || "");
  const { data: availableLists = [] } = useListsBySpaceQuery(user?.id || "", currentSpaceId);
  const { data: taskCounts = {} } = useTaskCountsQuery(user?.id || "");
  const addTaskMutation = useAddTaskMutation(selectedListId || "", sortOption);
  const createTagMutation = useCreateTagMutation(user?.id || "", currentSpaceId);
  const createPicklistTagMutation = useCreatePicklistTagMutation(user?.id || "", currentSpaceId);

  // Usage banner hook
  const { showUsageBanner } = useUsageBanner();

  // Reset selected list and status when dialog opens or listId changes
  useEffect(() => {
    if (open && listId) {
      setSelectedListId(listId);
    }
    if (open) {
      setSelectedStatus(initialStatus);
    }
  }, [open, listId, initialStatus]);

  const handleTagSelect = (tag: Tag) => {
    setSelectedTags(prev => {
      // Prevent duplicate selection
      if (prev.some(selectedTag => selectedTag.id === tag.id)) {
        console.log("Tag already selected:", tag.name);
        return prev;
      }
      return [...prev, tag];
    });
  };

  const handleTagRemove = (tag: Tag) => {
    setSelectedTags(prev => prev.filter(t => t.id !== tag.id));
  };

  const handleTagCreate = async (name: string, color: string): Promise<Tag | null> => {
    if (!user) return null;

    try {
      const newTag = await createTagMutation.mutateAsync({ name, color });
      // TanStack Query automatically updates the tags cache
      return newTag;
    } catch (error) {
      console.error('Error creating tag:', error);
      return null;
    }
  };

  const handlePicklistTagCreate = async (data: CreateTagData): Promise<Tag | null> => {
    if (!user) return null;

    try {
      if (data.type === 'picklist') {
        const newTag = await createPicklistTagMutation.mutateAsync({
          name: data.name,
          color: data.color,
          initialValues: data.initialValues,
        });
        return newTag;
      } else {
        // Fallback to regular tag creation
        return await handleTagCreate(data.name, data.color);
      }
    } catch (error) {
      console.error('Error creating picklist tag:', error);
      return null;
    }
  };

  const handleSearchTags = useCallback(async (searchTerm: string): Promise<Tag[]> => {
    if (!user) return [];

    try {
      return await searchTags(user.id, currentSpaceId, searchTerm);
    } catch (error) {
      console.error('Error searching tags:', error);
      return [];
    }
  }, [user]);

  const handleListSelect = (newListId: string) => {
    setSelectedListId(newListId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    if (!user) {
      setError("You must be logged in to add a task");
      return;
    }

    if (!listId) {
      setError("No list selected");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await addTaskMutation.mutateAsync({
        userId: user.id,
        title: title.trim(),
        description: description.trim() || undefined,
        due_date: dueDate || undefined,
        tagIds: selectedTags.map(tag => tag.id),
        status: selectedStatus,
      });

      if (result) {
        // Reset form and close dialog
        setTitle("");
        setDescription("");
        setDueDate(undefined);
        setSelectedTags([]);
        setSelectedListId(listId || ""); // Reset to original list
        onOpenChange(false);
        // Pass the new task data for optimistic updates
        onTaskAdded(result);
      } else {
        setError("Failed to create task. Please try again.");
      }
    } catch (err) {
      console.error("Error creating task:", err);

      // Check if it's a usage limit error
      if (err instanceof Error && err.message === 'USAGE_LIMIT_EXCEEDED') {
        showUsageBanner(getUsageLimitMessage('task'));
        setError(""); // Clear form error since we're showing banner
      } else {
        setError("An error occurred. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state when dialog is closed
    setTitle("");
    setDescription("");
    setDueDate(undefined);
    setSelectedTags([]);
    setSelectedListId(listId || ""); // Reset to original list
    setError("");
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[640px]" fullHeight>
        <MobileDialogHeader className="flex items-start justify-between px-4 pt-4 pb-0 md:px-6 md:pt-2">
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-6 w-6" />
              <span className="sr-only">Back</span>
            </Button>
            <VisuallyHidden asChild>
              <MobileDialogTitle>New Task</MobileDialogTitle>
            </VisuallyHidden>
          </div>

          {/* List + Status row */}
          <div className="flex items-center justify-between w-full">
              {/* List selector */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button type="button" variant="outline" size="sm" className="h-8 px-3 text-sm font-medium gap-1">
                    <span className="truncate max-w-[180px]">
                      {availableLists.find(l => l.id === selectedListId)?.name || "Select List"}
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="max-h-[200px] overflow-y-auto z-[80]">
                  {availableLists.map(list => (
                    <DropdownMenuItem
                      key={list.id}
                      onClick={(e) => { e.stopPropagation(); handleListSelect(list.id); }}
                      className="flex items-center gap-2 px-3 py-2 cursor-pointer"
                    >
                      <span className="truncate max-w-[140px] font-medium text-sm">{list.name}</span>
                      {(taskCounts?.[list.id] || 0) > 0 && (
                        <Badge className="text-xs border-transparent opacity-80 ml-auto">{taskCounts[list.id]}</Badge>
                      )}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Right aligned Status dropdown (defaults to Not Started) */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button type="button" variant="outline" size="sm" className="h-8 px-3 text-sm font-medium gap-2">
                    <span className="inline-flex items-center gap-2">
                      <span className="-ml-1"><StatusSquare status={selectedStatus} size="sm" /></span>
                      <span className="capitalize">{selectedStatus.replace('_', ' ')}</span>
                    </span>
                    <ChevronDown className="h-4 w-4 ml-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="z-[80]">
                  {([
                    { id: 'not_started', label: 'Not Started' },
                    { id: 'in_progress', label: 'In Progress' },
                    { id: 'completed', label: 'Completed' },
                  ] as const).map(opt => (
                    <DropdownMenuItem
                      key={opt.id}
                      onClick={(e) => { e.stopPropagation(); setSelectedStatus(opt.id); }}
                      className="flex items-center gap-2"
                    >
                      <StatusSquare status={opt.id as any} size="sm" />
                      <span>{opt.label}</span>
                      {opt.id === selectedStatus && <Check className="h-4 w-4 ml-auto" />}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </MobileDialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 px-4 pb-4 md:px-6 min-w-0">
            <div className="grid gap-2 min-w-0">
              <Input
                id="title"
                type="search"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyDown={(e) => {
                  // Prevent Enter from submitting the form
                  if (e.key === 'Enter') {
                    e.preventDefault();
                  }
                }}
                placeholder="Task title"
                autoComplete="off"
                spellCheck="false"
                maxLength={200}
              />
            </div>

            <div className="grid gap-2 min-w-0">
              <RichTextEditor
                id="description"
                value={description}
                onChange={setDescription}
                placeholder="Add details about this task (optional)"
                maxLength={10000}
              />
            </div>

            <div className="grid gap-2 min-w-0">
              <DateTimePicker
                date={dueDate}
                setDate={setDueDate}
                placeholder="Select due date"
                includeTime={true}
              />
            </div>

            <div className="grid gap-2">
              {/* Tags Display with Inline Add Button */}
              <div className="flex flex-wrap gap-1.5">
                {selectedTags.map((tag) => (
                  <TagPill
                    key={tag.id}
                    tag={tag}
                    onRemove={() => handleTagRemove(tag)}
                    size="sm"
                    showRemove={true}
                    allowInlineEdit={false}
                  />
                ))}

                {/* Inline Tag Picker for Adding New Tags */}
                <InlineTagPicker
                  selectedTags={selectedTags}
                  availableTags={availableTags}
                  onTagSelect={handleTagSelect}
                  onTagRemove={handleTagRemove}
                  onTagCreate={handleTagCreate}
                  onSearchTags={handleSearchTags}
                  onCreatePicklistTag={handlePicklistTagCreate}
                  size="sm"
                />
              </div>
            </div>

            {error && (
              <p className="text-sm font-medium text-destructive">{error}</p>
            )}
          </div>

          <div className="flex justify-end px-4 pb-4 md:px-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className="text-sm font-semibold text-muted-foreground hover:text-foreground disabled:text-muted-foreground/50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Creating..." : "Create"}
            </button>
          </div>
        </form>
      </MobileDialogContent>
    </MobileDialog>
  );
}
