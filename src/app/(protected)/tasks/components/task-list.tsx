"use client";

import { useState, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { Task, TaskSortOption } from "@/lib/db";
import { useReorderTasksMutation } from "@/lib/queries";
import { TaskItem } from "./task-item";
import { QuickAddTask } from "./quick-add-task";
import { useDroppable, DndContext, DragEndEvent, DragOverlay, useDndMonitor } from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useDragSensors,
  customCollisionDetection,
  createDragStartHandler,
  createDragOverHandler,
} from "@/lib/drag-and-drop";

interface TaskListProps {

  tasks: Task[]; // Parent tasks only (filtered)
  allTasks?: Task[]; // All tasks including subtasks (for TaskItem to find its subtasks)
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  sortOption: TaskSortOption;
  listId: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  showQuickAdd?: boolean;
  onAddTaskClick?: () => void;
  initialTaskStatus?: 'not_started' | 'in_progress' | 'completed';
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
  onDragStart?: () => void; // Callback when drag operation starts
  currentSpaceId?: string; // Current space ID for filtering lists
  externalDndContainerId?: string; // When provided, this list participates in a parent DndContext
}

export function TaskList({
  tasks,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  sortOption,
  listId,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  showQuickAdd = false,
  onAddTaskClick,
  initialTaskStatus = 'not_started',
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
  onDragStart,
  currentSpaceId,
  externalDndContainerId,
}: TaskListProps) {
  const user = useUser();
  // Local state for drag and drop visual feedback only
  const [isDragging, setIsDragging] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const lastDragOverRef = useRef<string | null>(null);

  // Helper: perform in-list reorder and mutate
  const reorderWithinList = (activeId: string, overId: string) => {
    if (activeId === overId) return;
    const oldIndex = tasks.findIndex((t) => t.id === activeId);
    const newIndex = tasks.findIndex((t) => t.id === overId);
    if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) return;
    const newTasks = arrayMove(tasks, oldIndex, newIndex);
    onTasksReordered(newTasks);
    if (user) {
      const taskIds = newTasks.map(t => t.id);
      reorderTasksMutation.mutate({ userId: user.id, taskIds });
    }
  };

  // TanStack Query mutation for reordering with optimistic updates
  const reorderTasksMutation = useReorderTasksMutation(listId, sortOption);

  // Use standardized drag and drop configuration
  const sensors = useDragSensors();
  // Register as a droppable container when participating in a parent DndContext
  const droppable = useDroppable({ id: externalDndContainerId || `tasklist-${listId}` });





  // Global monitor for parent DnD context (supports externalDndContainerId path)
  useDndMonitor({
    onDragStart: (event) => {
      const id = String(event.active.id);
      const found = tasks.find(t => t.id === id);
      if (found) {
        setIsDragging(true);
        setActiveTask(found);
      }
    },
    onDragEnd: (event) => {
      const activeId = String(event.active.id);
      const overId = event.over ? String(event.over.id) : "";
      // Only handle in-list reorder when both ids are in this TaskList
      if (tasks.some(t => t.id === activeId) && tasks.some(t => t.id === overId)) {
        reorderWithinList(activeId, overId);
      }
      setIsDragging(false);
      setActiveTask(null);
      lastDragOverRef.current = null;
    },
    onDragCancel: () => {
      setIsDragging(false);
      setActiveTask(null);
      lastDragOverRef.current = null;
    }
  });

  // Use standardized drag handlers
  const handleDragStart = createDragStartHandler(
    setIsDragging,
    setActiveTask,
    tasks,
    onDragStart,
    undefined, // No longer need isReorderingRef
    lastDragOverRef
  );

  const handleDragOver = createDragOverHandler(lastDragOverRef);

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Clean up drag state
    setIsDragging(false);
    setActiveTask(null);
    lastDragOverRef.current = null;

    // Validate drag operation - only proceed if there's actually a position change
    if (!over || !active || active.id === over.id) {
      return;
    }

    // Find the indices of the dragged and target tasks
    const oldIndex = tasks.findIndex((task) => task.id === active.id);
    const newIndex = tasks.findIndex((task) => task.id === over.id);

    // Validate indices
    if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) {
      return;
    }

    // Calculate new task order
    const newTasks = arrayMove(tasks, oldIndex, newIndex);
    const taskIds = newTasks.map(task => task.id);

    // Call the parent callback for undo/redo tracking
    onTasksReordered(newTasks);

    // Perform the server update with optimistic updates handled by TanStack Query
    if (user) {
      reorderTasksMutation.mutate({ userId: user.id, taskIds });
    }
  };

  return (
    <div className={`${isDragging ? 'dnd-context-dragging' : ''}`}>
      {sortOption === "position" ? (
        externalDndContainerId ? (
          // Participate in a parent DndContext
          <div ref={droppable.setNodeRef} className={`${droppable.isOver ? 'ring-1 ring-border/60 rounded-md' : ''}`}>
            <div className="space-y-2">
              <SortableContext items={tasks.map((task) => task.id)} strategy={verticalListSortingStrategy}>
                {tasks.map((task) => (
                  <TaskItem
                  key={task.id}
                  task={task}
                  onUpdated={onTaskUpdated}
                  onDeleted={onTaskDeleted}
                  isDraggable={true}
                  isAnyTaskDragging={isDragging}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                  allTasks={allTasks || tasks}
                  onNavigateToTask={onNavigateToTask}
                  isTagFiltered={isTagFiltered}
                  currentSpaceId={currentSpaceId}
                />
              ))}
            </SortableContext>
            {showQuickAdd && onAddTaskClick && (
              <QuickAddTask
                onAddTaskClick={onAddTaskClick}
                listId={listId}
                sortOption={sortOption}
                onTaskAdded={onTaskUpdated}
                initialStatus={initialTaskStatus}
              />
            )}
            </div>
            {/* Drag Overlay for parent DnD context participation */}
            <DragOverlay adjustScale={false} dropAnimation={null} style={{ transformOrigin: '0 0', transition: 'none' }}>
              {activeTask ? (
                <div className="drag-overlay task-drag-overlay">
                  <TaskItem
                    task={activeTask}
                    onUpdated={() => {}}
                    onDeleted={() => {}}
                    isDraggable={false}
                    isAnyTaskDragging={true}
                    listId={listId}
                    sortOption={sortOption}
                    listColor={listColor}
                    taskCounts={taskCounts}
                    taskMode={taskMode}
                    selectedTaskIds={selectedTaskIds}
                    onTaskSelectionChange={onTaskSelectionChange}
                    lastSelectedTaskId={lastSelectedTaskId}
                    forceSelected={true}
                    isInlineEditEnabled={isInlineEditEnabled}
                    activeActionIconsTaskId={activeActionIconsTaskId}
                    onActionIconsChange={onActionIconsChange}
                    allTasks={allTasks || tasks}
                    onNavigateToTask={onNavigateToTask}
                    isTagFiltered={isTagFiltered}
                    currentSpaceId={currentSpaceId}
                  />
                </div>
              ) : null}
            </DragOverlay>
          </div>
        ) : (
          <DndContext
            id={`parent-tasks-${listId}`}
            sensors={sensors}
            collisionDetection={customCollisionDetection}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToVerticalAxis]}
          >
            <div className="space-y-2">
              <SortableContext items={tasks.map((task) => task.id)} strategy={verticalListSortingStrategy}>
                {tasks.map((task) => (
                  <TaskItem
                  key={task.id}
                  task={task}
                  onUpdated={onTaskUpdated}
                  onDeleted={onTaskDeleted}
                  isDraggable={true}
                  isAnyTaskDragging={isDragging}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                  allTasks={allTasks || tasks}
                  onNavigateToTask={onNavigateToTask}
                  isTagFiltered={isTagFiltered}
                  currentSpaceId={currentSpaceId}
                />
                ))}
              </SortableContext>
              {showQuickAdd && onAddTaskClick && (
                <QuickAddTask
                  onAddTaskClick={onAddTaskClick}
                  listId={listId}
                  sortOption={sortOption}
                  onTaskAdded={onTaskUpdated}
                  initialStatus={initialTaskStatus}
                />
              )}
            </div>

            {/* Drag Overlay to prevent card compression and maintain visual consistency */}
            <DragOverlay adjustScale={false} dropAnimation={null} style={{ transformOrigin: '0 0', transition: 'none' }}>
              {activeTask ? (
                <div className="drag-overlay task-drag-overlay">
                  <TaskItem
                    task={activeTask}
                    onUpdated={() => {}}
                    onDeleted={() => {}}
                    isDraggable={false}
                    isAnyTaskDragging={true}
                    listId={listId}
                    sortOption={sortOption}
                    listColor={listColor}
                    taskCounts={taskCounts}
                    taskMode={taskMode}
                    selectedTaskIds={selectedTaskIds}
                    onTaskSelectionChange={onTaskSelectionChange}
                    lastSelectedTaskId={lastSelectedTaskId}
                    forceSelected={true}
                    isInlineEditEnabled={isInlineEditEnabled}
                    activeActionIconsTaskId={activeActionIconsTaskId}
                    onActionIconsChange={onActionIconsChange}
                    allTasks={allTasks || tasks}
                    onNavigateToTask={onNavigateToTask}
                    isTagFiltered={isTagFiltered}
                    currentSpaceId={currentSpaceId}
                  />
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
            )

      ) : (
        // Non-draggable list for when sorted by title or due date
        <div className="space-y-2">
        {tasks.map((task: Task) => (
          <TaskItem
            key={task.id}
            task={task}
            onUpdated={onTaskUpdated}
            onDeleted={onTaskDeleted}
            isDraggable={false}
            isAnyTaskDragging={false}
            listId={listId}
            sortOption={sortOption}
            listColor={listColor}
            taskCounts={taskCounts}
            taskMode={taskMode}
            selectedTaskIds={selectedTaskIds}
            onTaskSelectionChange={onTaskSelectionChange}
            lastSelectedTaskId={lastSelectedTaskId}
            isInlineEditEnabled={isInlineEditEnabled}
            activeActionIconsTaskId={activeActionIconsTaskId}
            onActionIconsChange={onActionIconsChange}
            allTasks={allTasks || tasks}
            onNavigateToTask={onNavigateToTask}
            isTagFiltered={isTagFiltered}
            currentSpaceId={currentSpaceId}
          />
        ))}
          {showQuickAdd && onAddTaskClick && (
            <QuickAddTask
            onAddTaskClick={onAddTaskClick}
            listId={listId}
            sortOption={sortOption}
            onTaskAdded={onTaskUpdated}
            initialStatus={initialTaskStatus}
          />
          )}
          </div>
      )}
    </div>
  );
}
