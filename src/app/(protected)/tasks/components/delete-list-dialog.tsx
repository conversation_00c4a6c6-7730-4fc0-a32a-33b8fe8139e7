"use client";

import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { removeList } from "@/app/actions/lists";
import { useUser } from "@stackframe/stack";
import { List } from "@/lib/db";

interface DeleteListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onListDeleted: () => void;
  list: List | null;
  taskCount: number;
}

export function DeleteListDialog({
  open,
  onOpenChange,
  onListDeleted,
  list,
  taskCount,
}: DeleteListDialogProps) {
  const user = useUser({ or: "redirect" });
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (user && list) {
      setIsDeleting(true);
      try {
        const success = await removeList(list.id, user.id);
        if (success) {
          onListDeleted();
        }
      } catch (error) {
        console.error("Error deleting list:", error);
      } finally {
        setIsDeleting(false);
      }
    }
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent aria-labelledby="delete-list-title">
        <AlertDialogTitle id="delete-list-title" className="sr-only">
          Delete List
        </AlertDialogTitle>
        <AlertDialogDescription>
          Are you sure you want to delete the list "{list?.name}"?
          {taskCount > 0 && (
            <>
              <br />
              <br />
              This will permanently delete {taskCount} task{taskCount !== 1 ? 's' : ''} in this list.
            </>
          )}
          <br />
          <br />
          This action cannot be undone.
        </AlertDialogDescription>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete List"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
