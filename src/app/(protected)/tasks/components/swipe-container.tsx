"use client";

import { useSwipeNavigation } from "@/hooks/use-swipe-navigation";
import { List } from "@/lib/db";

interface SwipeContainerProps {
  children: React.ReactNode;
  lists: List[];
  currentListId: string | null;
  onListChange: (list: List) => void;
  className?: string;
}

export function SwipeContainer({
  children,
  lists,
  currentListId,
  onListChange,
  className = "",
}: SwipeContainerProps) {
  // Find current list index
  const currentIndex = lists.findIndex(list => list.id === currentListId);

  const handleSwipeLeft = () => {
    // Swipe left = next list (higher index)
    if (currentIndex < lists.length - 1) {
      const nextList = lists[currentIndex + 1];
      // Change list immediately without animation
      onListChange(nextList);
    }
  };

  const handleSwipeRight = () => {
    // Swipe right = previous list (lower index)
    if (currentIndex > 0) {
      const prevList = lists[currentIndex - 1];
      // Change list immediately without animation
      onListChange(prevList);
    }
  };

  const { containerRef } = useSwipeNavigation({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 60, // Require 60px minimum swipe distance
    preventDefaultTouchMove: false, // Don't prevent default to avoid conflicts with scrolling
  });

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden min-h-screen ${className}`}
      style={{
        touchAction: 'pan-y', // Allow vertical scrolling but handle horizontal gestures
      }}
    >
      {children}
    </div>
  );
}
