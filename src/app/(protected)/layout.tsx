import { stackServerApp } from "@/stack";
import { syncUserWithDatabase } from "@/lib/auth-sync";
import { getUserSettings } from "@/lib/db";
import { redirect } from "next/navigation";
import { ClientLayout } from "./client-layout";

export default async function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await stackServerApp.getUser();

  if (!user) {
    redirect("/signin");
  }

  // Sync user data with our database
  await syncUserWithDatabase(user);

  // Get user settings for mascot preference
  // Use cache: 'no-store' to ensure we always get the latest settings
  const settings = await getUserSettings(user.id);
  const mascotPreference = settings?.mascot || "golden";

  return (
    <ClientLayout mascotPreference={mascotPreference as "golden" | "black"}>
      {children}
    </ClientLayout>
  );
}
