"use client";

import { useState, useEffect, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import Image from "next/image";

import { FloatingSaveButton } from "@/components/floating-save-button";
import { ThemeSelector } from "@/components/ui/theme-selector";
import { PrimaryColorPicker } from "@/components/ui/primary-color-picker";
import { useUserSettingsQuery, useUpdateSettingsMutation, useUserUsageQuery } from "@/lib/queries";
import { LoadingBoundary } from "@/components/ui/loading-boundary";
import { SkeletonCard } from "@/components/ui/skeleton";
import { usePrimaryColor } from "@/contexts/primary-color-context";
import { UsageProgress } from "@/components/ui/usage-progress";

export default function SettingsPage() {
  const user = useUser({ or: "redirect" });
  const router = useRouter();
  const { setPrimaryColor } = usePrimaryColor();

  // TanStack Query hooks
  const { data: userSettings, isLoading: isLoadingSettings } = useUserSettingsQuery(user?.id || "");
  const updateSettingsMutation = useUpdateSettingsMutation(user?.id || "");
  const { data: usageInfo, isLoading: isLoadingUsage } = useUserUsageQuery(user?.id || "");

  // Local state
  const [hasChanges, setHasChanges] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.displayName || "",
    email: user?.primaryEmail || "",
    notifications: true,
    week_starts_on: "sunday" as "sunday" | "monday",
    mascot: "golden" as "golden" | "black",
    primary_color: null as string | null,
  });



  // Store the original form data to detect changes
  const originalFormData = useRef({
    name: "",
    email: "",
    notifications: true,
    week_starts_on: "sunday" as "sunday" | "monday",
    mascot: "golden" as "golden" | "black",
    primary_color: null as string | null,
  });

  // Update form data when user settings are loaded
  useEffect(() => {
    if (user && userSettings) {
      const updatedFormData = {
        name: user.displayName || "",
        email: user.primaryEmail || "",
        notifications: userSettings.notifications_enabled,
        week_starts_on: (userSettings.week_starts_on as "sunday" | "monday") || "sunday",
        mascot: (userSettings.mascot as "golden" | "black") || "golden",
        primary_color: userSettings.primary_color || null,
      };

      setFormData(updatedFormData);

      // Store the original form data for change detection
      originalFormData.current = updatedFormData;
    }
  }, [user, userSettings]);

  // Check for changes whenever form data changes
  useEffect(() => {
    if (!isLoadingSettings) {
      const hasFormChanged =
        formData.name !== originalFormData.current.name ||
        formData.notifications !== originalFormData.current.notifications ||
        formData.week_starts_on !== originalFormData.current.week_starts_on ||
        formData.mascot !== originalFormData.current.mascot ||
        formData.primary_color !== originalFormData.current.primary_color;

      setHasChanges(hasFormChanged);
    }
  }, [formData, isLoadingSettings]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleRadioChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    try {
      // Update user profile
      await user.update({
        displayName: formData.name,
      });

      // Update user settings in our database using TanStack Query mutation
      // Theme is preserved from current settings
      const settingsToUpdate = {
        theme: userSettings?.theme || "system", // Preserve current theme
        notifications_enabled: formData.notifications,
        week_starts_on: formData.week_starts_on,
        mascot: formData.mascot,
        primary_color: formData.primary_color,
      };

      await updateSettingsMutation.mutateAsync(settingsToUpdate);

      // Update the primary color context
      setPrimaryColor(formData.primary_color);

      // Update the original form data to reflect the saved state
      originalFormData.current = { ...formData };

      // Reset the hasChanges flag
      setHasChanges(false);

      console.log("Settings updated successfully");
    } catch (error) {
      console.error("Error updating settings:", error);
    }
  };

  const handleSignOut = async () => {
    if (user) {
      await user.signOut();
      router.push("/handler/sign-in");
    }
  };

  if (!user) return null;

  return (
    <div className="px-2 md:px-4 py-3 space-y-6 pb-24">
      <Card className="gradient-border-muted">
        <CardHeader>
          <div className="flex flex-col gap-4">
            <h1 className="text-2xl font-light tracking-tight text-white">
              Settings
            </h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences.
            </p>
          </div>
        </CardHeader>
        <CardContent>
          <LoadingBoundary
            isLoading={isLoadingSettings}
            fallback={
              <div className="space-y-4">
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </div>
            }
            loadingText="Loading settings..."
          >
            <form onSubmit={handleSubmit}>
            <div className="space-y-2">
              <Card className="border">
                <CardHeader>
                  <CardTitle>Profile</CardTitle>
                  <CardDescription>
                    Update your personal information
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      Email cannot be changed
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="border">
                <CardHeader>
                  <CardTitle>Calendar Preferences</CardTitle>
                  <CardDescription>
                    Customize your calendar view
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="week_starts_on">Week Starts On</Label>
                    <div className="flex flex-col space-y-2 mt-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="sunday"
                          name="week_starts_on"
                          value="sunday"
                          checked={formData.week_starts_on === "sunday"}
                          onChange={(e) => handleRadioChange('week_starts_on', e.target.value)}
                          className="h-4 w-4 text-primary"
                          aria-labelledby="sunday-label"
                          title="Start week on Sunday"
                        />
                        <Label htmlFor="sunday" id="sunday-label">Sunday</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="monday"
                          name="week_starts_on"
                          value="monday"
                          checked={formData.week_starts_on === "monday"}
                          onChange={(e) => handleRadioChange('week_starts_on', e.target.value)}
                          className="h-4 w-4 text-primary"
                          aria-labelledby="monday-label"
                          title="Start week on Monday"
                        />
                        <Label htmlFor="monday" id="monday-label">Monday</Label>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Choose which day your week starts on in the calendar view
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="border">
                <CardHeader>
                  <CardTitle>Appearance</CardTitle>
                  <CardDescription>
                    Customize the look and feel of the app
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Primary Color</label>
                    <PrimaryColorPicker
                      value={formData.primary_color}
                      onChange={(color) => setFormData({ ...formData, primary_color: color })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Choose a color for buttons, links, and accents. Colorless uses a neutral theme.
                    </p>
                  </div>
                  <ThemeSelector />
                </CardContent>
              </Card>

              <Card className="border">
                <CardHeader>
                  <CardTitle>Mascot Preferences</CardTitle>
                  <CardDescription>
                    Choose your floating mascot
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="mascot">Select Mascot</Label>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          formData.mascot === "golden"
                            ? "border-white ring-2 ring-white/50"
                            : "border-border hover:border-white/50"
                        }`}
                        onClick={() => handleRadioChange('mascot', 'golden')}
                      >
                        <div className="flex flex-col items-center space-y-2">
                          <div className="relative w-24 h-24">
                            <Image
                              src="/sprites/golden.webp"
                              alt="Golden mascot"
                              fill
                              sizes="96px"
                              className="object-contain"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="golden"
                              name="mascot"
                              value="golden"
                              checked={formData.mascot === "golden"}
                              onChange={(e) => handleRadioChange('mascot', e.target.value)}
                              className="h-4 w-4 text-primary"
                              aria-labelledby="golden-label"
                              title="Select Golden mascot"
                            />
                            <Label htmlFor="golden" id="golden-label">Golden</Label>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          formData.mascot === "black"
                            ? "border-white ring-2 ring-white/50"
                            : "border-border hover:border-white/50"
                        }`}
                        onClick={() => handleRadioChange('mascot', 'black')}
                      >
                        <div className="flex flex-col items-center space-y-2">
                          <div className="relative w-24 h-24">
                            <Image
                              src="/sprites/black.webp"
                              alt="Black mascot"
                              fill
                              sizes="96px"
                              className="object-contain"
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="black"
                              name="mascot"
                              value="black"
                              checked={formData.mascot === "black"}
                              onChange={(e) => handleRadioChange('mascot', e.target.value)}
                              className="h-4 w-4 text-primary"
                              aria-labelledby="black-label"
                              title="Select Black mascot"
                            />
                            <Label htmlFor="black" id="black-label">Black</Label>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Choose which mascot appears in the bottom right corner
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="border">
                <CardHeader>
                  <CardTitle>Usage</CardTitle>
                  <CardDescription>
                    Your current usage across all owned spaces
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-6">
                  <LoadingBoundary
                    isLoading={isLoadingUsage}
                    fallback={<SkeletonCard className="h-32" />}
                    loadingText="Loading usage..."
                  >
                    {usageInfo && (
                      <div className="space-y-4">
                        <UsageProgress
                          label="Spaces"
                          current={usageInfo.current.spaces}
                          limit={usageInfo.limits.SPACES}
                        />
                        <UsageProgress
                          label="Lists"
                          current={usageInfo.current.lists}
                          limit={usageInfo.limits.LISTS}
                        />
                        <UsageProgress
                          label="Tasks"
                          current={usageInfo.current.tasks}
                          limit={usageInfo.limits.TASKS}
                        />
                      </div>
                    )}
                  </LoadingBoundary>
                </CardContent>
              </Card>

              <Card className="border">
                <CardHeader>
                  <CardTitle>Account</CardTitle>
                  <CardDescription>
                    Manage your account
                  </CardDescription>
                </CardHeader>
                <CardContent className="py-2 px-5 md:px-4 space-y-4">
                  <div className="space-y-2">
                    <Label>Sign Out</Label>
                    <p className="text-sm text-muted-foreground">
                      Sign out of your account on this device
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="destructive" onClick={handleSignOut}>
                    Sign Out
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </form>

          {/* Floating save button */}
          <FloatingSaveButton
            hasChanges={hasChanges}
            isLoading={updateSettingsMutation.isPending}
            onClick={() => {
              const fakeEvent = { preventDefault: () => {} } as React.FormEvent;
              handleSubmit(fakeEvent);
            }}
          />
          </LoadingBoundary>
        </CardContent>
      </Card>
    </div>
  );
}
