"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { BottomNavigation } from "@/components/bottom-navigation";
import { FloatingMascot } from "@/components/floating-mascot";
import { Sidebar } from "@/components/sidebar";
import { ListColorProvider } from "@/contexts/list-color-context";
import { PrimaryColorProvider } from "@/contexts/primary-color-context";
import { TagFilterProvider } from "@/contexts/tag-filter-context";
import { SidebarProvider, useSidebar } from "@/contexts/sidebar-context";
import { ChatSidebarProvider, useChatSidebar } from "@/contexts/chat-sidebar-context";
import { SpaceProvider, useSpace } from "@/contexts/space-context";
import { SpaceNavigationModal } from "@/components/spaces/space-navigation-modal";
import { CreateSpaceModal } from "@/components/spaces/create-space-modal";
import { ChatSidebar } from "@/components/chat-sidebar";
import { ChatToggleButton } from "@/components/chat-toggle-button";
import { MobileChatModal } from "@/components/mobile-chat-modal";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { UsageBannerProvider } from "@/components/ui/usage-banner";
import { useAppInitialization } from "@/hooks/use-app-initialization";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

interface ClientLayoutProps {
  children: React.ReactNode;
  mascotPreference: "golden" | "black";
}

// Inner component that uses sidebar context
function ClientLayoutInner({
  children,
  mascotPreference
}: ClientLayoutProps) {
  const pathname = usePathname();
  const isTasksPage = pathname === "/tasks";
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed, toggleCollapse } = useSidebar();
  const { isOpen: isChatOpen, toggleOpen: toggleChat, width: chatWidth, setWidth: setChatWidth } = useChatSidebar();

  // Track transition state to prevent text reflow during animations
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Custom toggle function that manages transition state
  const handleChatToggle = () => {
    setIsTransitioning(true);
    toggleChat();

    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 350); // Slightly longer than transition duration for safety
  };

  // Also track when chat width changes during resize
  useEffect(() => {
    if (isChatOpen) {
      setIsTransitioning(true);
      const timeoutId = setTimeout(() => {
        setIsTransitioning(false);
      }, 100); // Shorter timeout for resize operations

      return () => clearTimeout(timeoutId);
    }
  }, [chatWidth, isChatOpen]);
  const {
    currentSpace,
    handleSpaceClick,
    isSpaceLoading,
    isSpaceNavigationOpen,
    setIsSpaceNavigationOpen,
    isCreateSpaceOpen,
    setIsCreateSpaceOpen,
    handleSpaceSelect,
    handleCreateSpaceClick,
    handleSpaceCreated,
    handleSpaceUpdated
  } = useSpace();

  // Initialize app with critical data preloading as early as possible
  useAppInitialization();

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar */}
      {isDesktop && (
        <Sidebar
          isCollapsed={isCollapsed}
          onToggleCollapse={toggleCollapse}
          currentSpace={currentSpace}
          onSpaceClick={handleSpaceClick}
          isSpaceLoading={isSpaceLoading}
        />
      )}

      {/* Main Content Area */}
      <div
        className={cn(
          "flex min-h-screen flex-col flex-1 transition-all duration-300 ease-out",
          // Apply sidebar margins immediately since we have correct initial state
          isDesktop && "ml-64",
          isDesktop && isCollapsed && "ml-16",
          // Prevent content jumping during transitions
          isTransitioning && "overflow-hidden"
        )}
        style={{
          // Apply chat margin immediately since we have correct initial state
          marginRight: isDesktop && isChatOpen ? `${chatWidth}px` : '0px',
          // Smooth transition with better easing
          transition: isTransitioning ? 'margin-right 300ms cubic-bezier(0.4, 0, 0.2, 1)' : undefined
        }}
      >
        {/* Mobile Navigation - only show on mobile and when not on tasks page */}
        {!isDesktop && !isTasksPage && <Navigation />}

        <main className="flex-1 pb-16 md:pb-0">
          <div className={cn(
            // Only apply container constraint for non-tasks pages on desktop
            // Tasks page handles its own container constraints
            !isTasksPage && "container-max-width"
          )}>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        {!isDesktop && <BottomNavigation />}

        <FloatingMascot
          defaultMascot={mascotPreference}
        />

        {/* Space Modals */}
        <SpaceNavigationModal
          open={isSpaceNavigationOpen}
          onOpenChange={setIsSpaceNavigationOpen}
          currentSpaceId={currentSpace?.id}
          onSpaceSelect={handleSpaceSelect}
          onCreateSpaceClick={handleCreateSpaceClick}
          onSpaceUpdated={handleSpaceUpdated}
        />

        <CreateSpaceModal
          open={isCreateSpaceOpen}
          onOpenChange={setIsCreateSpaceOpen}
          onSpaceCreated={handleSpaceCreated}
        />
      </div>

      {/* Chat Interface - Desktop Sidebar or Mobile Modal */}
      {isDesktop ? (
        <>
          <ChatSidebar
            isOpen={isChatOpen}
            onToggle={handleChatToggle}
            width={chatWidth}
            onWidthChange={setChatWidth}
          />
          <ChatToggleButton
            isOpen={isChatOpen}
            onClick={handleChatToggle}
            chatWidth={chatWidth}
          />
        </>
      ) : (
        <>
          <MobileChatModal
            open={isChatOpen}
            onOpenChange={toggleChat}
          />
          <ChatToggleButton
            isOpen={isChatOpen}
            onClick={toggleChat}
          />
        </>
      )}
    </div>
  );
}

export function ClientLayout({
  children,
  mascotPreference
}: ClientLayoutProps) {
  return (
    <ErrorBoundary>
      <PrimaryColorProvider>
        <ListColorProvider>
          <TagFilterProvider>
            <SidebarProvider>
              <ChatSidebarProvider>
                <SpaceProvider>
                  <UsageBannerProvider>
                    <ClientLayoutInner
                      mascotPreference={mascotPreference}
                    >
                      {children}
                    </ClientLayoutInner>
                  </UsageBannerProvider>
                </SpaceProvider>
              </ChatSidebarProvider>
            </SidebarProvider>
          </TagFilterProvider>
        </ListColorProvider>
      </PrimaryColorProvider>
    </ErrorBoundary>
  );
}
