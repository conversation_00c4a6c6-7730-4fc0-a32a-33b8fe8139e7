"use client";

import { useState, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Task, UserSettings } from "@/lib/db";
import { fetchTasks } from "@/app/actions/tasks";
import { CalendarView } from "./components/calendar-view";
import { CalendarIcon, ViewIcon } from "lucide-react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { LoadingBoundary } from "@/components/ui/loading-boundary";
import { SkeletonCard } from "@/components/ui/skeleton";

type CalendarViewType = "month" | "week";

export default function CalendarPage() {
  const user = useUser({ or: "redirect" });
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewType, setViewType] = useState<CalendarViewType>("month");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState<"sunday" | "monday">("sunday");

  useEffect(() => {
    if (user) {
      loadTasks();
      loadUserSettings();
    }
  }, [user]);

  const loadUserSettings = async () => {
    try {
      const response = await fetch("/api/settings");
      if (response.ok) {
        const settings = await response.json() as UserSettings;
        setWeekStartsOn((settings.week_starts_on as "sunday" | "monday") || "sunday");
      }
    } catch (error) {
      console.error("Error loading user settings:", error);
    }
  };

  const loadTasks = async () => {
    setIsLoading(true);
    try {
      const fetchedTasks = await fetchTasks(user.id, "due_date");
      // Only include tasks with due dates
      const tasksWithDueDates = fetchedTasks.filter(task => task.due_date);
      setTasks(tasksWithDueDates);
    } catch (error) {
      console.error("Error loading tasks:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewChange = (value: string) => {
    if (value === "month" || value === "week") {
      setViewType(value);
    }
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  return (
    <div className="px-2 md:px-4 py-3 space-y-6">
      <div className="flex justify-start items-center">
        <ToggleGroup type="single" value={viewType} onValueChange={handleViewChange}>
          <ToggleGroupItem value="month">
            <CalendarIcon className="h-4 w-4 mr-2" />
            Month
          </ToggleGroupItem>
          <ToggleGroupItem value="week">
            <ViewIcon className="h-4 w-4 mr-2" />
            Week
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <Card className="gradient-border-muted">
        <CardContent className="pt-6">
          <LoadingBoundary
            isLoading={isLoading}
            fallback={<SkeletonCard />}
            loadingText="Loading calendar..."
          >
            <CalendarView
              tasks={tasks}
              viewType={viewType}
              currentDate={currentDate}
              setCurrentDate={setCurrentDate}
              weekStartsOn={weekStartsOn}
            />
          </LoadingBoundary>
        </CardContent>
      </Card>
    </div>
  );
}
