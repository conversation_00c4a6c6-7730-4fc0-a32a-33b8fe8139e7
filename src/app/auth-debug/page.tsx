"use client";

import { useEffect, useState } from "react";

interface AuthCheckResponse {
  authenticated: boolean;
  user: {
    id: string;
    email: string;
    name: string;
  } | null;
  stack: {
    projectId: string;
    clientKeyConfigured: boolean;
    serverKeyConfigured: boolean;
  };
  cookies?: {
    names: string[];
  };
  error?: string;
}

export default function AuthDebugPage() {
  const [authStatus, setAuthStatus] = useState<AuthCheckResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkAuth() {
      try {
        setIsLoading(true);
        const response = await fetch("/api/auth-check");
        
        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        setAuthStatus(data);
      } catch (err) {
        console.error("Error checking auth:", err);
        setError(err instanceof Error ? err.message : String(err));
      } finally {
        setIsLoading(false);
      }
    }

    checkAuth();
  }, []);

  const handleClearCookies = () => {
    // Clear all cookies
    document.cookie.split(';').forEach(cookie => {
      const [name] = cookie.trim().split('=');
      if (name) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
      }
    });
    
    // Reload the page
    window.location.reload();
  };

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Authentication Debug Page</h1>
      
      {isLoading ? (
        <div className="text-center">Loading...</div>
      ) : error ? (
        <div className="p-4 bg-red-100 text-red-800 rounded-md mb-4">
          <h2 className="font-semibold">Error:</h2>
          <p>{error}</p>
        </div>
      ) : authStatus ? (
        <div className="space-y-6">
          <div className="p-4 bg-gray-100 rounded-md">
            <h2 className="font-semibold text-xl mb-2">Authentication Status:</h2>
            <p className={authStatus.authenticated ? "text-green-600" : "text-red-600"}>
              {authStatus.authenticated ? "Authenticated" : "Not authenticated"}
            </p>
            
            {authStatus.authenticated && authStatus.user && (
              <div className="mt-2">
                <h3 className="font-medium">User Details:</h3>
                <ul className="list-disc pl-5">
                  <li>ID: {authStatus.user.id}</li>
                  <li>Email: {authStatus.user.email}</li>
                  <li>Name: {authStatus.user.name || "Not set"}</li>
                </ul>
              </div>
            )}
            
            {authStatus.error && (
              <div className="mt-2 p-2 bg-red-100 text-red-800 rounded">
                <h3 className="font-medium">Error:</h3>
                <p>{authStatus.error}</p>
              </div>
            )}
          </div>
          
          <div className="p-4 bg-gray-100 rounded-md">
            <h2 className="font-semibold text-xl mb-2">Stack Auth Configuration:</h2>
            <ul className="list-disc pl-5">
              <li>Project ID: {authStatus.stack.projectId || "Not set"}</li>
              <li>
                Client Key: {authStatus.stack.clientKeyConfigured ? "Configured" : "Not configured"}
              </li>
              <li>
                Server Key: {authStatus.stack.serverKeyConfigured ? "Configured" : "Not configured"}
              </li>
            </ul>
          </div>
          
          {authStatus.cookies && (
            <div className="p-4 bg-gray-100 rounded-md">
              <h2 className="font-semibold text-xl mb-2">Cookies:</h2>
              {authStatus.cookies.names.length > 0 ? (
                <ul className="list-disc pl-5">
                  {authStatus.cookies.names.map((name) => (
                    <li key={name}>{name}</li>
                  ))}
                </ul>
              ) : (
                <p>No cookies found</p>
              )}
            </div>
          )}
          
          <div className="flex space-x-4">
            <button
              onClick={handleClearCookies}
              className="px-4 py-2 bg-red-500 text-white rounded-md"
            >
              Clear All Cookies
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Refresh
            </button>
          </div>
        </div>
      ) : (
        <div className="text-center">No data available</div>
      )}
    </div>
  );
}
