"use client";

import { ReactNode } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface AuthWrapperProps {
  children: ReactNode;
  title: string;
  description: string;
}

export function AuthWrapper({ children, title, description }: AuthWrapperProps) {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md mx-auto gradient-border">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-light tracking-tight gradient-red-purple gradient-text">
            {title}
          </CardTitle>
          <CardDescription>
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>
    </div>
  );
}
