import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@stackframe/stack";
import { stackServerApp } from "@/stack";
import { CustomSignIn } from "./custom-sign-in";
import { CustomSignUp } from "./custom-sign-up";
import { CustomForgotPassword } from "./custom-forgot-password";

// Using a simpler approach to avoid type errors
export default function Handler(props: any) {
  return (
    <StackHandler
      app={stackServerApp}
      routeProps={props}
      components={{
        SignIn: CustomSignIn,
        SignUp: CustomSignUp,
        ForgotPassword: CustomForgotPassword,
      }}
    />
  );
}
