"use client";

import { useEffect, useState } from "react";
import { useStackApp } from "@stackframe/stack";

export default function AuthTestPage() {
  const stackApp = useStackApp();
  const [authState, setAuthState] = useState<{
    isAuthenticated: boolean;
    userId: string | null;
    error: string | null;
  }>({
    isAuthenticated: false,
    userId: null,
    error: null,
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function checkAuth() {
      try {
        setIsLoading(true);
        const session = await stackApp.auth.getSession();
        
        if (session) {
          setAuthState({
            isAuthenticated: true,
            userId: session.user.id,
            error: null,
          });
        } else {
          setAuthState({
            isAuthenticated: false,
            userId: null,
            error: null,
          });
        }
      } catch (error) {
        console.error("Error checking auth:", error);
        setAuthState({
          isAuthenticated: false,
          userId: null,
          error: error instanceof Error ? error.message : String(error),
        });
      } finally {
        setIsLoading(false);
      }
    }

    checkAuth();
  }, [stackApp]);

  const handleSignIn = async () => {
    try {
      setIsLoading(true);
      await stackApp.auth.signIn({
        provider: "credentials",
        email: "<EMAIL>",
        password: "password123",
      });
    } catch (error) {
      console.error("Sign-in error:", error);
      setAuthState({
        ...authState,
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setIsLoading(true);
      await stackApp.auth.signOut();
      setAuthState({
        isAuthenticated: false,
        userId: null,
        error: null,
      });
    } catch (error) {
      console.error("Sign-out error:", error);
      setAuthState({
        ...authState,
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4">Authentication Test Page</h1>
      
      {isLoading ? (
        <div className="text-center">Loading...</div>
      ) : (
        <div className="space-y-4">
          <div className="p-4 bg-gray-100 rounded-md">
            <h2 className="font-semibold">Authentication Status:</h2>
            <p>
              {authState.isAuthenticated
                ? `Authenticated (User ID: ${authState.userId})`
                : "Not authenticated"}
            </p>
          </div>

          {authState.error && (
            <div className="p-4 bg-red-100 text-red-800 rounded-md">
              <h2 className="font-semibold">Error:</h2>
              <p>{authState.error}</p>
            </div>
          )}

          <div className="flex space-x-4">
            {!authState.isAuthenticated ? (
              <button
                onClick={handleSignIn}
                className="px-4 py-2 bg-blue-500 text-white rounded-md"
                disabled={isLoading}
              >
                Sign In
              </button>
            ) : (
              <button
                onClick={handleSignOut}
                className="px-4 py-2 bg-red-500 text-white rounded-md"
                disabled={isLoading}
              >
                Sign Out
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
