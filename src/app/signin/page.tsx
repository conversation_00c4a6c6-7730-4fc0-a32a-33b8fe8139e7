"use client";

import { SignIn } from "@stackframe/stack";
import Image from "next/image";

export default function CustomSignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      {/* Container with max width for entire sign-in content */}
      <div className="w-full max-w-[380px] flex flex-col items-center">
        {/* Large NeoTask Logo at the top */}
        <div className="mb-8">
          <Image
            src="/NeoTask_Logo_white.webp"
            alt="NeoTask"
            width={160}
            height={160}
            className="w-32 h-32 md:w-40 md:h-40 object-contain"
            priority
          />
        </div>

        {/* Stack Auth Sign In Component */}
        <div className="w-full">
          <SignIn />
        </div>
      </div>
    </div>
  );
}
