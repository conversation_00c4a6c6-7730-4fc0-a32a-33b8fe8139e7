"use server";

import { unstable_cache, revalidateTag } from "next/cache";
import { UserSettings, getUserSettings, updateUserSettings } from "@/lib/db";

export async function fetchUserSettings(userId: string): Promise<UserSettings | null> {
  console.log("Server action: fetchUserSettings called with userId:", userId);
  const settings = await getUserSettings(userId);
  console.log("Server action: fetchUserSettings returning:", settings);
  return settings;
}

export async function updateSettings(
  userId: string,
  data: Partial<UserSettings>
): Promise<UserSettings | null> {
  console.log("Server action: updateSettings called with userId:", userId, "data:", data);
  const settings = await updateUserSettings(userId, data);

  // Invalidate cache when settings are updated
  revalidateTag("userSettings");

  console.log("Server action: updateSettings returning:", settings);
  return settings;
}
