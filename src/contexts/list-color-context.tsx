"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ListColorContextType {
  currentListColor: string | null;
  setCurrentListColor: (color: string | null) => void;
}

const ListColorContext = createContext<ListColorContextType | undefined>(undefined);

export function ListColorProvider({ children }: { children: ReactNode }) {
  const [currentListColor, setCurrentListColor] = useState<string | null>(null);

  return (
    <ListColorContext.Provider value={{ currentListColor, setCurrentListColor }}>
      {children}
    </ListColorContext.Provider>
  );
}

export function useListColor() {
  const context = useContext(ListColorContext);
  if (context === undefined) {
    throw new Error('useListColor must be used within a ListColorProvider');
  }
  return context;
}
