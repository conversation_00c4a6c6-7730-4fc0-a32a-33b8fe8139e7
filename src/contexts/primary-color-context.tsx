"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useUser } from "@stackframe/stack";
import { useUserSettingsQuery } from "@/lib/queries";

interface PrimaryColorContextType {
  primaryColor: string | null;
  setPrimaryColor: (color: string | null) => void;
  isLoading: boolean;
}

const PrimaryColorContext = createContext<PrimaryColorContextType | undefined>(undefined);

export function PrimaryColorProvider({ children }: { children: React.ReactNode }) {
  const user = useUser();
  const { data: userSettings, isLoading } = useUserSettingsQuery(user?.id || "");
  const [primaryColor, setPrimaryColorState] = useState<string | null>(null);

  // Update primary color when user settings change
  useEffect(() => {
    if (userSettings) {
      setPrimaryColorState(userSettings.primary_color || null);
    }
  }, [userSettings]);

  // Apply CSS variables when primary color changes
  useEffect(() => {
    const root = document.documentElement;
    
    if (primaryColor) {
      // Convert hex to OKLCH for consistency with existing color system
      const oklchColor = hexToOklch(primaryColor);
      const oklchColorDark = adjustOklchBrightness(oklchColor, 1.2); // Brighter for dark mode
      
      // Update CSS variables
      root.style.setProperty('--primary', oklchColor);
      root.style.setProperty('--primary-dark', oklchColorDark);
      
      // Update data attribute for CSS targeting
      root.setAttribute('data-primary-color', primaryColor);
    } else {
      // Reset to default (colorless)
      root.style.removeProperty('--primary');
      root.style.removeProperty('--primary-dark');
      root.removeAttribute('data-primary-color');
    }
  }, [primaryColor]);

  const setPrimaryColor = (color: string | null) => {
    setPrimaryColorState(color);
  };

  return (
    <PrimaryColorContext.Provider
      value={{
        primaryColor,
        setPrimaryColor,
        isLoading,
      }}
    >
      {children}
    </PrimaryColorContext.Provider>
  );
}

export function usePrimaryColor() {
  const context = useContext(PrimaryColorContext);
  if (context === undefined) {
    throw new Error("usePrimaryColor must be used within a PrimaryColorProvider");
  }
  return context;
}

// Helper function to convert hex to OKLCH (simplified)
function hexToOklch(hex: string): string {
  // This is a simplified conversion - in a real app you might want to use a proper color library
  // For now, we'll map common neon colors to their OKLCH equivalents
  const colorMap: Record<string, string> = {
    '#ff073a': 'oklch(0.55 0.25 0)',     // Neon Red
    '#ff6600': 'oklch(0.65 0.25 30)',    // Neon Orange  
    '#ffff00': 'oklch(0.85 0.25 90)',    // Neon Yellow
    '#ccff00': 'oklch(0.8 0.25 120)',    // Neon Lime
    '#00ff00': 'oklch(0.7 0.25 150)',    // Neon Green
    '#00ff99': 'oklch(0.7 0.25 180)',    // Neon Mint
    '#00ffff': 'oklch(0.75 0.25 210)',   // Neon Cyan
    '#0099ff': 'oklch(0.6 0.25 240)',    // Neon Sky (light blue)
    '#0066ff': 'oklch(0.55 0.25 250)',   // Neon Blue
    '#3300ff': 'oklch(0.5 0.25 260)',    // Neon Indigo
    '#6600ff': 'oklch(0.5 0.25 270)',    // Neon Purple
    '#cc00ff': 'oklch(0.55 0.25 300)',   // Neon Magenta
    '#ff00cc': 'oklch(0.6 0.25 320)',    // Neon Pink
    '#ff0066': 'oklch(0.6 0.25 340)',    // Neon Rose
    '#ff3366': 'oklch(0.65 0.25 350)',   // Neon Coral
    '#9933ff': 'oklch(0.55 0.25 280)',   // Neon Violet
  };
  
  return colorMap[hex] || 'oklch(0.5 0.25 240)'; // Default to light blue
}

// Helper function to adjust OKLCH brightness
function adjustOklchBrightness(oklch: string, factor: number): string {
  // Extract lightness value and multiply by factor
  const match = oklch.match(/oklch\(([0-9.]+)\s+([0-9.]+)\s+([0-9.]+)\)/);
  if (match) {
    const lightness = Math.min(1, parseFloat(match[1]) * factor);
    return `oklch(${lightness} ${match[2]} ${match[3]})`;
  }
  return oklch;
}
