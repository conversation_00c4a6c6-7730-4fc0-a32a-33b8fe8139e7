"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useUser } from "@stackframe/stack";
import { Space } from "@/lib/db";
import { useOwnedSpacesQuery, useSharedSpacesQuery, useUserSettingsQuery } from "@/lib/queries";
import { useTagFilter } from "@/contexts/tag-filter-context";

interface SpaceContextType {
  currentSpace: { id: string; name: string; icon?: string | null } | null;
  setCurrentSpace: (space: { id: string; name: string; icon?: string | null } | null) => void;
  isSpaceLoading: boolean;
  spaces: Space[];
  handleSpaceClick: () => void;
  isSpaceNavigationOpen: boolean;
  setIsSpaceNavigationOpen: (open: boolean) => void;
  isCreateSpaceOpen: boolean;
  setIsCreateSpaceOpen: (open: boolean) => void;
  handleSpaceSelect: (space: { id: string; name: string; icon?: string | null }) => void;
  handleCreateSpaceClick: () => void;
  handleSpaceCreated: (newSpace: { id: string; name: string; icon?: string | null }) => void;
  handleSpaceUpdated: (updatedSpace: Space) => void;
}

const SpaceContext = createContext<SpaceContextType | undefined>(undefined);

interface SpaceProviderProps {
  children: ReactNode;
}

export function SpaceProvider({ children }: SpaceProviderProps) {
  const user = useUser();
  const { clearTagFilter } = useTagFilter();
  const [currentSpace, setCurrentSpace] = useState<{ id: string; name: string; icon?: string | null } | null>(null);
  const [isSpaceNavigationOpen, setIsSpaceNavigationOpen] = useState(false);
  const [isCreateSpaceOpen, setIsCreateSpaceOpen] = useState(false);

  // Fetch spaces and user settings
  const { data: ownedSpaces = [], isLoading: ownedSpacesLoading } = useOwnedSpacesQuery(user?.id || "");
  const { data: sharedSpaces = [], isLoading: sharedSpacesLoading } = useSharedSpacesQuery(user?.id || "");
  const { data: userSettings, isLoading: userSettingsLoading } = useUserSettingsQuery(user?.id || "");

  // Combine owned and shared spaces
  const spaces = [...ownedSpaces, ...sharedSpaces];
  const spacesLoading = ownedSpacesLoading || sharedSpacesLoading;

  // Set current space when spaces and user settings are loaded
  useEffect(() => {
    if (spaces.length > 0 && !currentSpace && userSettings) {
      let defaultSpace: Space | undefined;

      // First, try to find the user's configured default space
      if (userSettings.default_space_id) {
        defaultSpace = spaces.find(space => space.id === userSettings.default_space_id);
      }

      // If no default space is configured or found, fall back to the first space
      if (!defaultSpace) {
        defaultSpace = spaces[0];
      }

      setCurrentSpace(defaultSpace);
    }
  }, [spaces, currentSpace, userSettings]);

  // Calculate space loading state
  const isSpaceLoading = Boolean(
    spacesLoading ||
    userSettingsLoading ||
    (spaces.length > 0 && userSettings && !currentSpace)
  );

  // Space handlers
  const handleSpaceClick = () => {
    setIsSpaceNavigationOpen(true);
  };

  const handleSpaceSelect = (space: { id: string; name: string; icon?: string | null }) => {
    setCurrentSpace(space);
    // Clear tag filter when switching spaces
    clearTagFilter();
    // Note: List clearing is handled by the tasks page when currentSpace changes
  };

  const handleCreateSpaceClick = () => {
    setIsCreateSpaceOpen(true);
  };

  const handleSpaceCreated = (newSpace: { id: string; name: string; icon?: string | null }) => {
    setCurrentSpace(newSpace);
    // Clear tag filter when switching to new space
    clearTagFilter();
    // Note: List clearing is handled by the tasks page when currentSpace changes
  };

  const handleSpaceUpdated = (updatedSpace: Space) => {
    // Update current space if it's the one that was updated
    if (currentSpace && currentSpace.id === updatedSpace.id) {
      setCurrentSpace({
        id: updatedSpace.id,
        name: updatedSpace.name,
        icon: updatedSpace.icon
      });
    }
  };

  return (
    <SpaceContext.Provider value={{
      currentSpace,
      setCurrentSpace,
      isSpaceLoading,
      spaces,
      handleSpaceClick,
      isSpaceNavigationOpen,
      setIsSpaceNavigationOpen,
      isCreateSpaceOpen,
      setIsCreateSpaceOpen,
      handleSpaceSelect,
      handleCreateSpaceClick,
      handleSpaceCreated,
      handleSpaceUpdated,
    }}>
      {children}
    </SpaceContext.Provider>
  );
}

export function useSpace() {
  const context = useContext(SpaceContext);
  if (context === undefined) {
    throw new Error("useSpace must be used within a SpaceProvider");
  }
  return context;
}
