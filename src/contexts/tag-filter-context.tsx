"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { TagFilter, TagFilterState } from '@/lib/types';

interface TagFilterContextType {
  tagFilterState: TagFilterState;
  setActiveTag: (tag: TagFilter | null) => void;
  clearTagFilter: () => void;
}

const TagFilterContext = createContext<TagFilterContextType | undefined>(undefined);

export function TagFilterProvider({ children }: { children: ReactNode }) {
  const [tagFilterState, setTagFilterState] = useState<TagFilterState>({
    activeTag: null,
    isActive: false,
  });

  const setActiveTag = (tag: TagFilter | null) => {
    setTagFilterState({
      activeTag: tag,
      isActive: tag !== null,
    });
  };

  const clearTagFilter = () => {
    setTagFilterState({
      activeTag: null,
      isActive: false,
    });
  };

  return (
    <TagFilterContext.Provider value={{ tagFilterState, setActiveTag, clearTagFilter }}>
      {children}
    </TagFilterContext.Provider>
  );
}

export function useTagFilter() {
  const context = useContext(TagFilterContext);
  if (context === undefined) {
    throw new Error('useTagFilter must be used within a TagFilterProvider');
  }
  return context;
}
