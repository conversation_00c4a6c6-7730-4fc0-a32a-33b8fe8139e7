"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface ChatSidebarContextType {
  isOpen: boolean;
  toggleOpen: () => void;
  setOpen: (open: boolean) => void;
  width: number;
  setWidth: (width: number) => void;
  isHydrated: boolean;
}

const ChatSidebarContext = createContext<ChatSidebarContextType | undefined>(undefined);

interface ChatSidebarProviderProps {
  children: ReactNode;
}

export function ChatSidebarProvider({ children }: ChatSidebarProviderProps) {
  // Initialize with functions to avoid hydration mismatch
  const [isOpen, setIsOpen] = useState(() => {
    // During SSR, always return false (closed)
    if (typeof window === 'undefined') return false;

    // During hydration, try to get from localStorage immediately
    try {
      const savedState = localStorage.getItem("chat-sidebar-open");
      return savedState !== null ? JSON.parse(savedState) : false;
    } catch {
      return false;
    }
  });

  const [width, setWidth] = useState(() => {
    // During SSR, return default width
    if (typeof window === 'undefined') return 320;

    // During hydration, try to get from localStorage immediately
    try {
      const savedWidth = localStorage.getItem("chat-sidebar-width");
      return savedWidth !== null ? JSON.parse(savedWidth) : 320;
    } catch {
      return 320;
    }
  });

  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after mount
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Save chat sidebar state to localStorage when it changes (only after hydration)
  useEffect(() => {
    if (isHydrated) {
      localStorage.setItem("chat-sidebar-open", JSON.stringify(isOpen));
    }
  }, [isOpen, isHydrated]);

  // Save chat sidebar width to localStorage when it changes (only after hydration)
  useEffect(() => {
    if (isHydrated) {
      localStorage.setItem("chat-sidebar-width", JSON.stringify(width));
    }
  }, [width, isHydrated]);

  const toggleOpen = () => {
    setIsOpen(prev => !prev);
  };

  const setOpen = (open: boolean) => {
    setIsOpen(open);
  };

  return (
    <ChatSidebarContext.Provider value={{ isOpen, toggleOpen, setOpen, width, setWidth, isHydrated }}>
      {children}
    </ChatSidebarContext.Provider>
  );
}

export function useChatSidebar() {
  const context = useContext(ChatSidebarContext);
  if (context === undefined) {
    throw new Error("useChatSidebar must be used within a ChatSidebarProvider");
  }
  return context;
}
