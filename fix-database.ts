// <PERSON>ript to fix the database schema
import { db } from './src/lib/db';
import { sql } from 'drizzle-orm';

async function fixDatabase() {
  try {
    console.log('Checking and fixing tags table schema...');
    
    // Add the type column if it doesn't exist
    await db.execute(sql`ALTER TABLE tags ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'regular'`);
    console.log('✅ Type column added/verified');
    
    // Check the table structure
    const columns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'tags' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Current tags table structure:');
    console.table(columns.rows);
    
    // Show sample tags
    const sampleTags = await db.execute(sql`SELECT id, name, type, created_at FROM tags LIMIT 5`);
    console.log('📝 Sample tags:');
    console.table(sampleTags.rows);
    
    console.log('✅ Database schema check complete!');
    
  } catch (error) {
    console.error('❌ Error fixing database:', error);
  }
}

fixDatabase();
