// NeoTask Service Worker - Auth-Safe PWA Implementation
// This service worker is designed to NOT interfere with Stack Auth

const CACHE_NAME = 'neotask-v1';
const STATIC_CACHE_NAME = 'neotask-static-v1';

// Only cache static assets - NO auth-related routes
const STATIC_ASSETS = [
  '/NeoTask_Logo_white.webp',
  '/NeoTask_Icon_N.webp',
  '/NeoTask Icon Black 192.png',
  '/NeoTask Icon Black 512.png',
  '/sprites/black.webp',
  '/sprites/golden.webp',
  // Add other static assets as needed
];

// Routes to NEVER cache (critical for auth to work)
const NEVER_CACHE_PATTERNS = [
  /^\/api\//, // All API routes
  /^\/handler\//, // Stack Auth handler routes
  /^\/signin/, // Sign in page
  /^\/signup/, // Sign up page
  /^\/auth/, // Any auth-related routes
  /\/_next\/static\/chunks\/pages\/api\//, // API chunks
  /\/auth-check/, // Auth check endpoint
  /\/auth-proxy/, // Auth proxy endpoint
];

// Check if a URL should never be cached
function shouldNeverCache(url) {
  const pathname = new URL(url).pathname;
  return NEVER_CACHE_PATTERNS.some(pattern => pattern.test(pathname));
}

// Install event - cache static assets only
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('[SW] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('[SW] Failed to cache static assets:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with auth-safe caching
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = request.url;

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // NEVER cache auth-related routes
  if (shouldNeverCache(url)) {
    console.log('[SW] Bypassing cache for auth route:', url);
    return; // Let the request go through normally
  }

  // Handle static assets with cache-first strategy
  if (STATIC_ASSETS.some(asset => url.includes(asset))) {
    event.respondWith(
      caches.match(request)
        .then((response) => {
          if (response) {
            console.log('[SW] Serving static asset from cache:', url);
            return response;
          }
          
          console.log('[SW] Fetching and caching static asset:', url);
          return fetch(request)
            .then((response) => {
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(STATIC_CACHE_NAME)
                  .then((cache) => cache.put(request, responseClone));
              }
              return response;
            });
        })
    );
    return;
  }

  // For HTML pages: Network-first strategy to ensure fresh auth state
  if (request.headers.get('accept')?.includes('text/html')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          console.log('[SW] Network-first for HTML page:', url);
          return response;
        })
        .catch((error) => {
          console.log('[SW] Network failed for HTML page, checking cache:', url);
          return caches.match(request)
            .then((response) => {
              if (response) {
                console.log('[SW] Serving HTML from cache:', url);
                return response;
              }
              throw error;
            });
        })
    );
    return;
  }

  // For other resources: Network-first with cache fallback
  event.respondWith(
    fetch(request)
      .then((response) => {
        if (response.status === 200) {
          const responseClone = response.clone();
          caches.open(CACHE_NAME)
            .then((cache) => cache.put(request, responseClone));
        }
        return response;
      })
      .catch(() => {
        return caches.match(request);
      })
  );
});

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

console.log('[SW] Service worker script loaded');
