{"id": "097282c2-eed1-4ba7-b80c-0b0a5dc06dc7", "prevId": "a60eca6f-8db3-4e97-b083-c21157c8326e", "version": "7", "dialect": "postgresql", "tables": {"public.lists": {"name": "lists", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lists_user_id_users_id_fk": {"name": "lists_user_id_users_id_fk", "tableFrom": "lists", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lists_space_id_spaces_id_fk": {"name": "lists_space_id_spaces_id_fk", "tableFrom": "lists", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"lists\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"lists\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"lists\".\"user_id\")", "withCheck": "(select auth.user_id() = \"lists\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"lists\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"spaces_user_id_users_id_fk": {"name": "spaces_user_id_users_id_fk", "tableFrom": "spaces", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"spaces\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"spaces\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"spaces\".\"user_id\")", "withCheck": "(select auth.user_id() = \"spaces\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"spaces\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tags_user_id_users_id_fk": {"name": "tags_user_id_users_id_fk", "tableFrom": "tags", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"tags\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"tags\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"tags\".\"user_id\")", "withCheck": "(select auth.user_id() = \"tags\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"tags\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_activities": {"name": "task_activities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "activity_type": {"name": "activity_type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_activities_user_id_users_id_fk": {"name": "task_activities_user_id_users_id_fk", "tableFrom": "task_activities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_activities_task_id_tasks_id_fk": {"name": "task_activities_task_id_tasks_id_fk", "tableFrom": "task_activities", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")", "withCheck": "(select auth.user_id() = \"task_activities\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"task_activities\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_tags": {"name": "task_tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_tags_task_id_tasks_id_fk": {"name": "task_tags_task_id_tasks_id_fk", "tableFrom": "task_tags", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_tags_tag_id_tags_id_fk": {"name": "task_tags_tag_id_tags_id_fk", "tableFrom": "task_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = \"task_tags\".\"task_id\" AND tasks.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = \"task_tags\".\"task_id\" AND tasks.user_id = auth.user_id()\n      ) AND EXISTS (\n        SELECT 1 FROM tags\n        WHERE tags.id = \"task_tags\".\"tag_id\" AND tags.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = \"task_tags\".\"task_id\" AND tasks.user_id = auth.user_id()\n      ) AND EXISTS (\n        SELECT 1 FROM tags\n        WHERE tags.id = \"task_tags\".\"tag_id\" AND tags.user_id = auth.user_id()\n      )\n    )", "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = \"task_tags\".\"task_id\" AND tasks.user_id = auth.user_id()\n      ) AND EXISTS (\n        SELECT 1 FROM tags\n        WHERE tags.id = \"task_tags\".\"tag_id\" AND tags.user_id = auth.user_id()\n      )\n    )"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = \"task_tags\".\"task_id\" AND tasks.user_id = auth.user_id()\n      ) AND EXISTS (\n        SELECT 1 FROM tags\n        WHERE tags.id = \"task_tags\".\"tag_id\" AND tags.user_id = auth.user_id()\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "list_id": {"name": "list_id", "type": "text", "primaryKey": false, "notNull": true}, "parent_task_id": {"name": "parent_task_id", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'todo'"}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tasks_user_id_users_id_fk": {"name": "tasks_user_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_list_id_lists_id_fk": {"name": "tasks_list_id_lists_id_fk", "tableFrom": "tasks", "tableTo": "lists", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"tasks\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"tasks\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"tasks\".\"user_id\")", "withCheck": "(select auth.user_id() = \"tasks\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"tasks\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true, "default": "'system'"}, "notifications_enabled": {"name": "notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "week_starts_on": {"name": "week_starts_on", "type": "text", "primaryKey": false, "notNull": true, "default": "'monday'"}, "mascot": {"name": "mascot", "type": "text", "primaryKey": false, "notNull": true, "default": "'golden'"}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")", "withCheck": "(select auth.user_id() = \"user_settings\".\"user_id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"user_settings\".\"user_id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"crud-authenticated-policy-select": {"name": "crud-authenticated-policy-select", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-insert": {"name": "crud-authenticated-policy-insert", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-update": {"name": "crud-authenticated-policy-update", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")", "withCheck": "(select auth.user_id() = \"users\".\"id\")"}, "crud-authenticated-policy-delete": {"name": "crud-authenticated-policy-delete", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(select auth.user_id() = \"users\".\"id\")"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}