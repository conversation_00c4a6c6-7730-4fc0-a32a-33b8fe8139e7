{"id": "4b94933b-1d05-4e0a-960d-363e7987a9d7", "prevId": "1520d5d2-1695-4073-bc8e-bd1fc455019b", "version": "7", "dialect": "postgresql", "tables": {"public.lists": {"name": "lists", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lists_user_id_users_id_fk": {"name": "lists_user_id_users_id_fk", "tableFrom": "lists", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create lists": {"name": "create lists", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = user_id)"}, "view lists": {"name": "view lists", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "update lists": {"name": "update lists", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "delete lists": {"name": "delete lists", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tags_user_id_users_id_fk": {"name": "tags_user_id_users_id_fk", "tableFrom": "tags", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create tags": {"name": "create tags", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = user_id)"}, "view tags": {"name": "view tags", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "update tags": {"name": "update tags", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "delete tags": {"name": "delete tags", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_activities": {"name": "task_activities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "activity_type": {"name": "activity_type", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_activities_user_id_users_id_fk": {"name": "task_activities_user_id_users_id_fk", "tableFrom": "task_activities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_activities_task_id_tasks_id_fk": {"name": "task_activities_task_id_tasks_id_fk", "tableFrom": "task_activities", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create task_activities": {"name": "create task_activities", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = user_id)"}, "view task_activities": {"name": "view task_activities", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "update task_activities": {"name": "update task_activities", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "delete task_activities": {"name": "delete task_activities", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.task_tags": {"name": "task_tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"task_tags_task_id_tasks_id_fk": {"name": "task_tags_task_id_tasks_id_fk", "tableFrom": "task_tags", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "task_tags_tag_id_tags_id_fk": {"name": "task_tags_tag_id_tags_id_fk", "tableFrom": "task_tags", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create task_tags": {"name": "create task_tags", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()\n      ) AND EXISTS (\n        SELECT 1 FROM tags\n        WHERE tags.id = tag_id AND tags.user_id = auth.user_id()\n      )\n    )"}, "view task_tags": {"name": "view task_tags", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()\n      )\n    )"}, "update task_tags": {"name": "update task_tags", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()\n      )\n    )"}, "delete task_tags": {"name": "delete task_tags", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(\n      EXISTS (\n        SELECT 1 FROM tasks\n        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()\n      )\n    )"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tasks": {"name": "tasks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "list_id": {"name": "list_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'todo'"}, "position": {"name": "position", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"tasks_user_id_users_id_fk": {"name": "tasks_user_id_users_id_fk", "tableFrom": "tasks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_list_id_lists_id_fk": {"name": "tasks_list_id_lists_id_fk", "tableFrom": "tasks", "tableTo": "lists", "columnsFrom": ["list_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create tasks": {"name": "create tasks", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = user_id)"}, "view tasks": {"name": "view tasks", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "update tasks": {"name": "update tasks", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "delete tasks": {"name": "delete tasks", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true, "default": "'system'"}, "notifications_enabled": {"name": "notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "week_starts_on": {"name": "week_starts_on", "type": "text", "primaryKey": false, "notNull": true, "default": "'monday'"}, "mascot": {"name": "mascot", "type": "text", "primaryKey": false, "notNull": true, "default": "'golden'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"create user_settings": {"name": "create user_settings", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = user_id)"}, "view user_settings": {"name": "view user_settings", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "update user_settings": {"name": "update user_settings", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}, "delete user_settings": {"name": "delete user_settings", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = user_id)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"create users": {"name": "create users", "as": "PERMISSIVE", "for": "INSERT", "to": ["authenticated"], "withCheck": "(auth.user_id() = id)"}, "view users": {"name": "view users", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(auth.user_id() = id)"}, "update users": {"name": "update users", "as": "PERMISSIVE", "for": "UPDATE", "to": ["authenticated"], "using": "(auth.user_id() = id)"}, "delete users": {"name": "delete users", "as": "PERMISSIVE", "for": "DELETE", "to": ["authenticated"], "using": "(auth.user_id() = id)"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}