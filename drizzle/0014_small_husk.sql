CREATE TABLE "picklist_values" (
	"id" text PRIMARY KEY NOT NULL,
	"tag_id" text NOT NULL,
	"value" text NOT NULL,
	"position" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "picklist_values" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "space_collaborators" (
	"id" text PRIMARY KEY NOT NULL,
	"space_id" text NOT NULL,
	"email" text NOT NULL,
	"user_id" text,
	"joined_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "space_collaborators" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "task_picklist_selections" (
	"id" text PRIMARY KEY NOT NULL,
	"task_id" text NOT NULL,
	"tag_id" text NOT NULL,
	"picklist_value_id" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "task_picklist_selections" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "tags" ADD COLUMN "type" text DEFAULT 'regular' NOT NULL;--> statement-breakpoint
ALTER TABLE "picklist_values" ADD CONSTRAINT "picklist_values_tag_id_tags_id_fk" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "space_collaborators" ADD CONSTRAINT "space_collaborators_space_id_spaces_id_fk" FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "space_collaborators" ADD CONSTRAINT "space_collaborators_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_picklist_selections" ADD CONSTRAINT "task_picklist_selections_task_id_tasks_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_picklist_selections" ADD CONSTRAINT "task_picklist_selections_tag_id_tags_id_fk" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "task_picklist_selections" ADD CONSTRAINT "task_picklist_selections_picklist_value_id_picklist_values_id_fk" FOREIGN KEY ("picklist_value_id") REFERENCES "public"."picklist_values"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "picklist_values" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "picklist_values"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "picklist_values" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "picklist_values"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "picklist_values" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "picklist_values"."tag_id" AND tags.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "picklist_values"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "picklist_values" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "picklist_values"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "space_collaborators" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = "space_collaborators"."space_id")
      OR auth.user_id() = "space_collaborators"."user_id"
      OR (SELECT auth.email()) = "space_collaborators"."email"
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "space_collaborators" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = "space_collaborators"."space_id")
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "space_collaborators" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = "space_collaborators"."space_id")
    )) WITH CHECK ((
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = "space_collaborators"."space_id")
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "space_collaborators" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((
      SELECT auth.user_id() = (SELECT user_id FROM spaces WHERE id = "space_collaborators"."space_id")
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "task_picklist_selections" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_picklist_selections"."task_id" AND tasks.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "task_picklist_selections" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_picklist_selections"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_picklist_selections"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "task_picklist_selections" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_picklist_selections"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_picklist_selections"."tag_id" AND tags.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_picklist_selections"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_picklist_selections"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "task_picklist_selections" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_picklist_selections"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_picklist_selections"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));