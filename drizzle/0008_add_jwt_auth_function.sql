-- Ensure the auth schema exists
CREATE SCHEMA IF NOT EXISTS auth;

-- Update the auth.user_id() function to support JWT-based authentication
-- This function will first try to get the user ID from JWT claims, then fall back to session config

CREATE OR REPLACE FUNCTION auth.user_id()
RETURNS text
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  jwt_claims text;
  user_id_from_jwt text;
  user_id_from_session text;
BEGIN
  -- First, try to get user ID from JWT claims
  BEGIN
    -- Get the JWT claims from the request
    jwt_claims := current_setting('request.jwt.claims', true);

    -- If we have JWT claims, extract the user ID (sub claim)
    IF jwt_claims IS NOT NULL AND jwt_claims != '' THEN
      -- Parse the JWT claims JSON to get the 'sub' field
      user_id_from_jwt := jwt_claims::json->>'sub';

      -- Return the user ID from JWT if found
      IF user_id_from_jwt IS NOT NULL AND user_id_from_jwt != '' THEN
        RETURN user_id_from_jwt;
      END IF;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- If JWT parsing fails, continue to session fallback
      NULL;
  END;

  -- Fallback to session-based user ID (for backward compatibility)
  BEGIN
    user_id_from_session := current_setting('auth.user_id', true);
    IF user_id_from_session IS NOT NULL AND user_id_from_session != '' THEN
      RETURN user_id_from_session;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      -- If session config fails, return null
      NULL;
  END;

  -- If neither JWT nor session has user ID, return null
  RETURN NULL;
END;
$$;

-- Create a dedicated function for JWT-based user ID extraction
CREATE OR REPLACE FUNCTION auth.jwt_user_id()
RETURNS text
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  jwt_claims text;
  user_id_from_jwt text;
BEGIN
  -- Get the JWT claims from the request
  jwt_claims := current_setting('request.jwt.claims', true);
  
  -- If we have JWT claims, extract the user ID (sub claim)
  IF jwt_claims IS NOT NULL AND jwt_claims != '' THEN
    -- Parse the JWT claims JSON to get the 'sub' field
    user_id_from_jwt := jwt_claims::json->>'sub';
    
    -- Return the user ID from JWT if found
    IF user_id_from_jwt IS NOT NULL AND user_id_from_jwt != '' THEN
      RETURN user_id_from_jwt;
    END IF;
  END IF;
  
  -- Return null if no valid JWT user ID found
  RETURN NULL;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION auth.user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.user_id() TO public;
GRANT EXECUTE ON FUNCTION auth.jwt_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION auth.jwt_user_id() TO public;
