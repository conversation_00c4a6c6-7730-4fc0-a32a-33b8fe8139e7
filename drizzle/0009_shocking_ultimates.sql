DROP POLICY "create lists" ON "lists" CASCADE;--> statement-breakpoint
DROP POLICY "view lists" ON "lists" CASCADE;--> statement-breakpoint
DROP POLICY "update lists" ON "lists" CASCADE;--> statement-breakpoint
DROP POLICY "delete lists" ON "lists" CASCADE;--> statement-breakpoint
DROP POLICY "create tags" ON "tags" CASCADE;--> statement-breakpoint
DROP POLICY "view tags" ON "tags" CASCADE;--> statement-breakpoint
DROP POLICY "update tags" ON "tags" CASCADE;--> statement-breakpoint
DROP POLICY "delete tags" ON "tags" CASCADE;--> statement-breakpoint
DROP POLICY "create task_activities" ON "task_activities" CASCADE;--> statement-breakpoint
DROP POLICY "view task_activities" ON "task_activities" CASCADE;--> statement-breakpoint
DROP POLICY "update task_activities" ON "task_activities" CASCADE;--> statement-breakpoint
DROP POLICY "delete task_activities" ON "task_activities" CASCADE;--> statement-breakpoint
DROP POLICY "create task_tags" ON "task_tags" CASCADE;--> statement-breakpoint
DROP POLICY "view task_tags" ON "task_tags" CASCADE;--> statement-breakpoint
DROP POLICY "update task_tags" ON "task_tags" CASCADE;--> statement-breakpoint
DROP POLICY "delete task_tags" ON "task_tags" CASCADE;--> statement-breakpoint
DROP POLICY "create tasks" ON "tasks" CASCADE;--> statement-breakpoint
DROP POLICY "view tasks" ON "tasks" CASCADE;--> statement-breakpoint
DROP POLICY "update tasks" ON "tasks" CASCADE;--> statement-breakpoint
DROP POLICY "delete tasks" ON "tasks" CASCADE;--> statement-breakpoint
DROP POLICY "create user_settings" ON "user_settings" CASCADE;--> statement-breakpoint
DROP POLICY "view user_settings" ON "user_settings" CASCADE;--> statement-breakpoint
DROP POLICY "update user_settings" ON "user_settings" CASCADE;--> statement-breakpoint
DROP POLICY "delete user_settings" ON "user_settings" CASCADE;--> statement-breakpoint
DROP POLICY "create users" ON "users" CASCADE;--> statement-breakpoint
DROP POLICY "view users" ON "users" CASCADE;--> statement-breakpoint
DROP POLICY "update users" ON "users" CASCADE;--> statement-breakpoint
DROP POLICY "delete users" ON "users" CASCADE;--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "lists" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "lists"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "lists" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "lists"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "lists" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "lists"."user_id")) WITH CHECK ((select auth.user_id() = "lists"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "lists" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "lists"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "tags" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "tags"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "tags" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "tags"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "tags" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "tags"."user_id")) WITH CHECK ((select auth.user_id() = "tags"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "tags" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "tags"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "task_activities" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "task_activities"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "task_activities" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "task_activities"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "task_activities" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "task_activities"."user_id")) WITH CHECK ((select auth.user_id() = "task_activities"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "task_activities" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "task_activities"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "task_tags" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_tags"."task_id" AND tasks.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "task_tags" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_tags"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_tags"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "task_tags" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_tags"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_tags"."tag_id" AND tags.user_id = auth.user_id()
      )
    )) WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_tags"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_tags"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "task_tags" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = "task_tags"."task_id" AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = "task_tags"."tag_id" AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "tasks" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "tasks"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "tasks" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "tasks"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "tasks" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "tasks"."user_id")) WITH CHECK ((select auth.user_id() = "tasks"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "tasks" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "tasks"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "user_settings" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "user_settings"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "user_settings" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "user_settings"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "user_settings" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "user_settings"."user_id")) WITH CHECK ((select auth.user_id() = "user_settings"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "user_settings" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "user_settings"."user_id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-select" ON "users" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = "users"."id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-insert" ON "users" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = "users"."id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-update" ON "users" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = "users"."id")) WITH CHECK ((select auth.user_id() = "users"."id"));--> statement-breakpoint
CREATE POLICY "crud-authenticated-policy-delete" ON "users" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = "users"."id"));