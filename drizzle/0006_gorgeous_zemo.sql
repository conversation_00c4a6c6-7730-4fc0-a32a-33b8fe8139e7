ALTER TABLE "lists" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "tags" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "task_activities" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "task_tags" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "tasks" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_settings" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE POLICY "create lists" ON "lists" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "view lists" ON "lists" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "update lists" ON "lists" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "delete lists" ON "lists" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "create tags" ON "tags" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "view tags" ON "tags" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "update tags" ON "tags" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "delete tags" ON "tags" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "create task_activities" ON "task_activities" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "view task_activities" ON "task_activities" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "update task_activities" ON "task_activities" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "delete task_activities" ON "task_activities" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "create task_tags" ON "task_tags" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()
      ) AND EXISTS (
        SELECT 1 FROM tags
        WHERE tags.id = tag_id AND tags.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "view task_tags" ON "task_tags" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "update task_tags" ON "task_tags" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "delete task_tags" ON "task_tags" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((
      EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = task_id AND tasks.user_id = auth.user_id()
      )
    ));--> statement-breakpoint
CREATE POLICY "create tasks" ON "tasks" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "view tasks" ON "tasks" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "update tasks" ON "tasks" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "delete tasks" ON "tasks" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "create user_settings" ON "user_settings" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "view user_settings" ON "user_settings" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "update user_settings" ON "user_settings" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "delete user_settings" ON "user_settings" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = user_id));--> statement-breakpoint
CREATE POLICY "create users" ON "users" AS PERMISSIVE FOR INSERT TO "authenticated" WITH CHECK ((select auth.user_id() = id));--> statement-breakpoint
CREATE POLICY "view users" ON "users" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.user_id() = id));--> statement-breakpoint
CREATE POLICY "update users" ON "users" AS PERMISSIVE FOR UPDATE TO "authenticated" USING ((select auth.user_id() = id));--> statement-breakpoint
CREATE POLICY "delete users" ON "users" AS PERMISSIVE FOR DELETE TO "authenticated" USING ((select auth.user_id() = id));