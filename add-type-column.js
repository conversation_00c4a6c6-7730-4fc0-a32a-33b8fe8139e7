// Quick script to add the type column to the tags table
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('DATABASE_URL environment variable is required');
  process.exit(1);
}

const sql = postgres(connectionString);
const db = drizzle(sql);

async function addTypeColumn() {
  try {
    console.log('Adding type column to tags table...');
    
    // Add the type column with default value 'regular'
    await sql`ALTER TABLE tags ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'regular'`;
    
    console.log('Type column added successfully!');
    
    // Check the table structure
    const result = await sql`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'tags' 
      ORDER BY ordinal_position
    `;
    
    console.log('Current tags table structure:');
    console.table(result);
    
  } catch (error) {
    console.error('Error adding type column:', error);
  } finally {
    await sql.end();
  }
}

addTypeColumn();
