#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { sql } from 'drizzle-orm';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
const db = drizzle(pool);

async function cleanupDuplicates() {
  console.log('Cleaning up duplicate emails...');

  try {
    // Find duplicate emails
    const duplicates = await db.execute(sql`
      SELECT email, COUNT(*) as count
      FROM users
      GROUP BY email
      HAVING COUNT(*) > 1
    `);

    console.log(`Found ${duplicates.rows.length} duplicate email groups`);

    for (const duplicate of duplicates.rows) {
      const email = duplicate.email as string;
      const count = duplicate.count as number;

      console.log(`Processing ${count} duplicates for email: ${email}`);

      // Get all users with this email, ordered by created_at (keep the oldest)
      const users = await db.execute(sql`
        SELECT id, created_at
        FROM users
        WHERE email = ${email}
        ORDER BY created_at ASC
      `);

      // Keep the first (oldest) user, delete the rest
      for (let i = 1; i < users.rows.length; i++) {
        const userId = users.rows[i].id as string;
        console.log(`Deleting duplicate user: ${userId}`);

        // Delete the user (cascade will handle related records)
        await db.execute(sql`DELETE FROM users WHERE id = ${userId}`);
      }
    }

    console.log('Cleanup completed successfully!');

  } catch (error) {
    console.error('Cleanup failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the cleanup
cleanupDuplicates().catch((error) => {
  console.error('Cleanup script failed:', error);
  process.exit(1);
});
