const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function createSpaceCollaboratorsTable() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('🚀 Connected to database');

    // Create the table
    console.log('Creating space_collaborators table...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS "space_collaborators" (
        "id" text PRIMARY KEY NOT NULL,
        "space_id" text NOT NULL,
        "email" text NOT NULL,
        "user_id" text,
        "joined_at" timestamp,
        "created_at" timestamp DEFAULT now() NOT NULL
      );
    `);

    // Add foreign key constraints
    console.log('Adding foreign key constraints...');
    try {
      await client.query(`
        ALTER TABLE "space_collaborators" 
        ADD CONSTRAINT "space_collaborators_space_id_spaces_id_fk" 
        FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") 
        ON DELETE cascade ON UPDATE no action;
      `);
    } catch (err) {
      if (!err.message.includes('already exists')) {
        throw err;
      }
      console.log('Foreign key constraint already exists');
    }

    try {
      await client.query(`
        ALTER TABLE "space_collaborators" 
        ADD CONSTRAINT "space_collaborators_user_id_users_id_fk" 
        FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") 
        ON DELETE cascade ON UPDATE no action;
      `);
    } catch (err) {
      if (!err.message.includes('already exists')) {
        throw err;
      }
      console.log('User foreign key constraint already exists');
    }

    // Add unique constraint
    console.log('Adding unique constraint...');
    try {
      await client.query(`
        ALTER TABLE "space_collaborators" 
        ADD CONSTRAINT "space_collaborators_space_id_email_unique" 
        UNIQUE ("space_id", "email");
      `);
    } catch (err) {
      if (!err.message.includes('already exists')) {
        throw err;
      }
      console.log('Unique constraint already exists');
    }

    // Enable RLS
    console.log('Enabling RLS...');
    await client.query(`ALTER TABLE "space_collaborators" ENABLE ROW LEVEL SECURITY;`);

    console.log('✅ Space collaborators table created successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await client.end();
  }
}

createSpaceCollaboratorsTable()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
