import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import * as schema from '../src/lib/db/schema';
import { eq, and, asc } from 'drizzle-orm';

// Initialize database connection
const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

async function setDefaultSpaces() {
  console.log('🚀 Setting default spaces for existing users...');

  try {
    // Get all users
    const users = await db.select().from(schema.users);
    console.log(`Found ${users.length} users`);

    for (const user of users) {
      console.log(`Processing user: ${user.email}`);

      // Get the user's first space (ordered by creation date)
      const firstSpace = await db.select()
        .from(schema.spaces)
        .where(eq(schema.spaces.user_id, user.id))
        .orderBy(asc(schema.spaces.created_at))
        .limit(1);

      if (firstSpace.length > 0) {
        const defaultSpaceId = firstSpace[0].id;
        console.log(`Setting default space for ${user.email}: ${firstSpace[0].name} (${defaultSpaceId})`);

        // Update user settings to set default space
        await db.update(schema.userSettings)
          .set({ 
            default_space_id: defaultSpaceId,
            updated_at: new Date()
          })
          .where(eq(schema.userSettings.user_id, user.id));

        console.log(`✅ Updated default space for ${user.email}`);
      } else {
        console.log(`⚠️ No spaces found for user ${user.email}`);
      }
    }

    console.log('✅ Default spaces migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

// Run the migration
setDefaultSpaces()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
