#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { sql } from 'drizzle-orm';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

async function testJWTAuth() {
  console.log('🧪 Testing JWT-based authentication...');

  // Test with the authenticated connection
  const authenticatedPool = new Pool({ connectionString: process.env.DATABASE_AUTHENTICATED_URL! });
  const authenticatedDb = drizzle(authenticatedPool);

  try {
    // Test 1: Check if we can connect to the authenticated database
    console.log('📡 Testing authenticated database connection...');
    const result = await authenticatedDb.execute(sql`SELECT 1 as test`);
    console.log('✅ Authenticated database connection successful');

    // Test 2: Check if RLS is enabled
    console.log('🔒 Testing RLS status...');
    const rlsStatus = await authenticatedDb.execute(sql`SHOW row_security`);
    console.log('🔒 RLS status:', rlsStatus);

    // Test 3: Try to access user data without authentication (should fail or return empty)
    console.log('🚫 Testing unauthenticated access...');
    try {
      const unauthenticatedResult = await authenticatedDb.execute(sql`SELECT COUNT(*) FROM users`);
      console.log('📊 Unauthenticated user count:', unauthenticatedResult);
    } catch (error) {
      console.log('✅ Unauthenticated access properly blocked:', error);
    }

    // Test 4: Test setting JWT claims (simulated)
    console.log('🔐 Testing JWT claims setting...');
    const testUserId = 'test-user-123';
    const testJWT = JSON.stringify({ sub: testUserId, iat: Date.now() });
    
    try {
      await authenticatedDb.execute(sql`SELECT set_config('request.jwt.claims', ${testJWT}, true)`);
      console.log('✅ JWT claims set successfully');
      
      // Test if auth.user_id() returns the correct user ID
      const userIdResult = await authenticatedDb.execute(sql`SELECT auth.user_id() as user_id`);
      console.log('👤 auth.user_id() result:', userIdResult);
      
    } catch (error) {
      console.log('❌ JWT claims test failed:', error);
    }

    console.log('✅ JWT authentication tests completed!');

  } catch (error) {
    console.error('❌ JWT authentication test failed:', error);
  } finally {
    await authenticatedPool.end();
  }
}

// Run the test
testJWTAuth().catch(console.error);
