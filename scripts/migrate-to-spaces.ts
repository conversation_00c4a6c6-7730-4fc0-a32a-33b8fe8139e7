#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { eq, sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import * as schema from '../src/lib/db/schema';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
const db = drizzle(pool, { schema });

async function migrateToSpaces() {
  console.log('Starting migration to spaces feature...');

  try {
    // Step 1: Create the spaces table
    console.log('Creating spaces table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "spaces" (
        "id" text PRIMARY KEY NOT NULL,
        "user_id" text NOT NULL,
        "name" text NOT NULL,
        "description" text,
        "icon" text,
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL
      );
    `);

    // Step 2: Enable RLS on spaces table
    console.log('Enabling RLS on spaces table...');
    await db.execute(sql`ALTER TABLE "spaces" ENABLE ROW LEVEL SECURITY;`);

    // Step 3: Create RLS policies for spaces
    console.log('Creating RLS policies for spaces...');
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-select" ON "spaces" 
      AS PERMISSIVE FOR SELECT TO "authenticated" 
      USING ((select auth.user_id() = "spaces"."user_id"));
    `);
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-insert" ON "spaces" 
      AS PERMISSIVE FOR INSERT TO "authenticated" 
      WITH CHECK ((select auth.user_id() = "spaces"."user_id"));
    `);
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-update" ON "spaces" 
      AS PERMISSIVE FOR UPDATE TO "authenticated" 
      USING ((select auth.user_id() = "spaces"."user_id")) 
      WITH CHECK ((select auth.user_id() = "spaces"."user_id"));
    `);
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-delete" ON "spaces" 
      AS PERMISSIVE FOR DELETE TO "authenticated" 
      USING ((select auth.user_id() = "spaces"."user_id"));
    `);

    // Step 4: Add foreign key constraint
    await db.execute(sql`
      ALTER TABLE "spaces" ADD CONSTRAINT "spaces_user_id_users_id_fk" 
      FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") 
      ON DELETE cascade ON UPDATE no action;
    `);

    // Step 5: Get all users and create default spaces
    console.log('Creating default spaces for all users...');
    const users = await db.select().from(schema.users);
    
    const defaultSpaces: { [userId: string]: string } = {};
    
    for (const user of users) {
      const spaceId = uuidv4();
      defaultSpaces[user.id] = spaceId;
      
      await db.insert(schema.spaces).values({
        id: spaceId,
        user_id: user.id,
        name: 'Tasks',
        description: 'Default space for all your tasks',
        icon: '📋',
      });
      
      console.log(`Created default space for user ${user.email}`);
    }

    // Step 6: Add space_id column to lists table (nullable first)
    console.log('Adding space_id column to lists table...');
    await db.execute(sql`ALTER TABLE "lists" ADD COLUMN "space_id" text;`);

    // Step 7: Update all existing lists to use the default space
    console.log('Updating existing lists with default space...');
    for (const user of users) {
      const defaultSpaceId = defaultSpaces[user.id];
      await db.execute(sql`
        UPDATE "lists" 
        SET "space_id" = ${defaultSpaceId} 
        WHERE "user_id" = ${user.id};
      `);
      console.log(`Updated lists for user ${user.email}`);
    }

    // Step 8: Make space_id NOT NULL and add foreign key constraint
    console.log('Making space_id NOT NULL and adding foreign key constraint...');
    await db.execute(sql`ALTER TABLE "lists" ALTER COLUMN "space_id" SET NOT NULL;`);
    await db.execute(sql`
      ALTER TABLE "lists" ADD CONSTRAINT "lists_space_id_spaces_id_fk" 
      FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") 
      ON DELETE cascade ON UPDATE no action;
    `);

    console.log('✅ Migration to spaces completed successfully!');
    console.log(`📊 Created ${users.length} default spaces`);
    console.log('📝 All existing lists have been associated with default spaces');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration
if (require.main === module) {
  migrateToSpaces().catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
}

export { migrateToSpaces };
