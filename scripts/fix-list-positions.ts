#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { eq, sql } from 'drizzle-orm';
import * as schema from '../src/lib/db/schema';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
const db = drizzle(pool, { schema });

async function fixListPositions() {
  console.log('Fixing list positions...');

  try {
    // Get all users
    const users = await db.select().from(schema.users);
    console.log(`Found ${users.length} users`);

    for (const user of users) {
      console.log(`Processing user: ${user.email}`);
      
      // Get all lists for this user ordered by created_at
      const userLists = await db.select()
        .from(schema.lists)
        .where(eq(schema.lists.user_id, user.id))
        .orderBy(schema.lists.created_at);

      console.log(`Found ${userLists.length} lists for user ${user.email}`);

      // Update each list with its position
      for (let i = 0; i < userLists.length; i++) {
        const list = userLists[i];
        const position = i + 1;
        
        await db.update(schema.lists)
          .set({ position, updated_at: new Date() })
          .where(eq(schema.lists.id, list.id));
        
        console.log(`Updated list "${list.name}" to position ${position}`);
      }
    }

    console.log('List positions fixed successfully!');

  } catch (error) {
    console.error('Failed to fix list positions:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
fixListPositions().catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
