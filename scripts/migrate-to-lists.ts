#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { eq, sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import * as schema from '../src/lib/db/schema';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
const db = drizzle(pool, { schema });

async function migrateToLists() {
  console.log('Starting migration to lists feature...');

  try {
    // Step 1: Check if lists table exists
    const tablesResult = await db.execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'lists'
    `);

    if (tablesResult.rows.length === 0) {
      console.log('Lists table does not exist. Please run the schema migration first.');
      console.log('Run: npx drizzle-kit push');
      return;
    }

    // Step 2: Check if list_id column exists in tasks table
    const columnsResult = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'tasks' AND column_name = 'list_id'
    `);

    if (columnsResult.rows.length === 0) {
      console.log('Tasks table does not have list_id column. Please run the schema migration first.');
      console.log('Run: npx drizzle-kit push');
      return;
    }

    // Step 3: Get all users
    const users = await db.select().from(schema.users);
    console.log(`Found ${users.length} users`);

    // Step 4: Create default "To Do" list for each user (if they don't have one)
    for (const user of users) {
      // Check if user already has a "To Do" list
      const existingList = await db.select()
        .from(schema.lists)
        .where(eq(schema.lists.user_id, user.id))
        .limit(1);

      let defaultListId: string;

      if (existingList.length === 0) {
        // Create default "To Do" list
        defaultListId = uuidv4();
        await db.insert(schema.lists).values({
          id: defaultListId,
          user_id: user.id,
          name: 'To Do',
        });
        console.log(`Created default "To Do" list for user ${user.email}`);
      } else {
        defaultListId = existingList[0].id;
        console.log(`User ${user.email} already has a list`);
      }

      // Step 5: Update tasks without list_id to use the default list
      const tasksWithoutList = await db.execute(sql`
        SELECT id FROM tasks
        WHERE user_id = ${user.id} AND (list_id IS NULL OR list_id = '')
      `);

      if (tasksWithoutList.rows.length > 0) {
        await db.execute(sql`
          UPDATE tasks
          SET list_id = ${defaultListId}, updated_at = NOW()
          WHERE user_id = ${user.id} AND (list_id IS NULL OR list_id = '')
        `);
        console.log(`Updated ${tasksWithoutList.rows.length} tasks for user ${user.email}`);
      }
    }

    console.log('Migration completed successfully!');

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration
migrateToLists().catch((error) => {
  console.error('Migration script failed:', error);
  process.exit(1);
});
