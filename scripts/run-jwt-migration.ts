#!/usr/bin/env tsx

import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool, neonConfig } from '@neondatabase/serverless';
import { sql } from 'drizzle-orm';
import * as fs from 'fs';
import * as path from 'path';
import ws from 'ws';

// Configure WebSocket for Neon
neonConfig.webSocketConstructor = ws;

async function runJWTMigration() {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
  const db = drizzle(pool);

  try {
    console.log('🔄 Running JWT authentication migration...');

    // Read the SQL migration file
    const migrationPath = path.join(process.cwd(), 'drizzle', '0008_add_jwt_auth_function.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    await db.execute(sql.raw(migrationSQL));

    console.log('✅ JWT authentication migration completed successfully!');
    console.log('📝 Updated auth.user_id() function to support JWT claims');
    console.log('📝 Added auth.jwt_user_id() function for JWT-only authentication');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the migration
runJWTMigration().catch(console.error);
