import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import { sql } from 'drizzle-orm';
import * as schema from '../src/lib/db/schema';

async function migrateSpaceCollaborators() {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL! });
  const db = drizzle(pool, { schema });

  console.log('🚀 Starting space collaborators migration...');

  try {
    // Step 1: Create the space_collaborators table
    console.log('Creating space_collaborators table...');
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "space_collaborators" (
        "id" text PRIMARY KEY NOT NULL,
        "space_id" text NOT NULL,
        "email" text NOT NULL,
        "user_id" text,
        "joined_at" timestamp,
        "created_at" timestamp DEFAULT now() NOT NULL
      );
    `);

    // Step 2: Add foreign key constraints
    console.log('Adding foreign key constraints...');
    await db.execute(sql`
      ALTER TABLE "space_collaborators" 
      ADD CONSTRAINT "space_collaborators_space_id_spaces_id_fk" 
      FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") 
      ON DELETE cascade ON UPDATE no action;
    `);

    await db.execute(sql`
      ALTER TABLE "space_collaborators" 
      ADD CONSTRAINT "space_collaborators_user_id_users_id_fk" 
      FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") 
      ON DELETE cascade ON UPDATE no action;
    `);

    // Step 3: Add unique constraint to prevent duplicate email invitations per space
    console.log('Adding unique constraint...');
    await db.execute(sql`
      ALTER TABLE "space_collaborators" 
      ADD CONSTRAINT "space_collaborators_space_id_email_unique" 
      UNIQUE ("space_id", "email");
    `);

    // Step 4: Enable RLS on space_collaborators table
    console.log('Enabling RLS on space_collaborators table...');
    await db.execute(sql`ALTER TABLE "space_collaborators" ENABLE ROW LEVEL SECURITY;`);

    // Step 5: Create RLS policies for space_collaborators
    console.log('Creating RLS policies for space_collaborators...');
    
    // Policy for SELECT: Space owners, collaborators, and invited users can read
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-select" ON "space_collaborators" 
      AS PERMISSIVE FOR SELECT TO "authenticated" 
      USING ((
        select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
        OR auth.user_id() = space_collaborators.user_id
        OR (select auth.email()) = space_collaborators.email
      ));
    `);

    // Policy for INSERT: Only space owners can add collaborators
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-insert" ON "space_collaborators" 
      AS PERMISSIVE FOR INSERT TO "authenticated" 
      WITH CHECK ((
        select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
      ));
    `);

    // Policy for UPDATE: Only space owners can update collaborators, or users can update their own records
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-update" ON "space_collaborators" 
      AS PERMISSIVE FOR UPDATE TO "authenticated" 
      USING ((
        select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
        OR (auth.user_id() = space_collaborators.user_id AND (select auth.email()) = space_collaborators.email)
      ));
    `);

    // Policy for DELETE: Only space owners can remove collaborators
    await db.execute(sql`
      CREATE POLICY "crud-authenticated-policy-delete" ON "space_collaborators" 
      AS PERMISSIVE FOR DELETE TO "authenticated" 
      USING ((
        select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
      ));
    `);

    console.log('✅ Space collaborators migration completed successfully!');
    console.log('📊 Created space_collaborators table with proper RLS policies');
    console.log('🔒 Added foreign key constraints and unique constraints');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateSpaceCollaborators()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { migrateSpaceCollaborators };
