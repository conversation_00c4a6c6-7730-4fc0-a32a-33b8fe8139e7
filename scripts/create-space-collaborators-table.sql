-- Create space_collaborators table
CREATE TABLE IF NOT EXISTS "space_collaborators" (
  "id" text PRIMARY KEY NOT NULL,
  "space_id" text NOT NULL,
  "email" text NOT NULL,
  "user_id" text,
  "joined_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "space_collaborators" 
ADD CONSTRAINT "space_collaborators_space_id_spaces_id_fk" 
FOREIGN KEY ("space_id") REFERENCES "public"."spaces"("id") 
ON DELETE cascade ON UPDATE no action;

ALTER TABLE "space_collaborators" 
ADD CONSTRAINT "space_collaborators_user_id_users_id_fk" 
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") 
ON DELETE cascade ON UPDATE no action;

-- Add unique constraint to prevent duplicate email invitations per space
ALTER TABLE "space_collaborators" 
ADD CONSTRAINT "space_collaborators_space_id_email_unique" 
UNIQUE ("space_id", "email");

-- Enable RLS on space_collaborators table
ALTER TABLE "space_collaborators" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for space_collaborators

-- Policy for SELECT: Space owners, collaborators, and invited users can read
CREATE POLICY "crud-authenticated-policy-select" ON "space_collaborators" 
AS PERMISSIVE FOR SELECT TO "authenticated" 
USING ((
  select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
  OR auth.user_id() = space_collaborators.user_id
  OR (select auth.email()) = space_collaborators.email
));

-- Policy for INSERT: Only space owners can add collaborators
CREATE POLICY "crud-authenticated-policy-insert" ON "space_collaborators" 
AS PERMISSIVE FOR INSERT TO "authenticated" 
WITH CHECK ((
  select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
));

-- Policy for UPDATE: Only space owners can update collaborators, or users can update their own records
CREATE POLICY "crud-authenticated-policy-update" ON "space_collaborators" 
AS PERMISSIVE FOR UPDATE TO "authenticated" 
USING ((
  select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
  OR (auth.user_id() = space_collaborators.user_id AND (select auth.email()) = space_collaborators.email)
));

-- Policy for DELETE: Only space owners can remove collaborators
CREATE POLICY "crud-authenticated-policy-delete" ON "space_collaborators" 
AS PERMISSIVE FOR DELETE TO "authenticated" 
USING ((
  select auth.user_id() = (SELECT user_id FROM spaces WHERE id = space_collaborators.space_id)
));
