-- Add the type column to the tags table
ALTER TABLE tags ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'regular';

-- Check the table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'tags' 
ORDER BY ordinal_position;

-- Show a few sample tags to verify the type column exists
SELECT id, name, type, created_at FROM tags LIMIT 5;
